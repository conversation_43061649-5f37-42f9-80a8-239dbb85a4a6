@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400&display=swap');


body {
    font-family: 'Poppins', sans-serif;
    font-weight: 400;
    /* Regular */
}

.tabs-wrapper {
    font-family: 'Poppins', sans-serif;
    background: #f8f9fa;
    padding: 10px;
}

.tabs-header {
    display: flex;
    gap: 20px;
    /* border-bottom: 1px solid #ddd; */
}

.tab-btn {
    background: none;
    border: none;
    padding: 10px 18px 10px 18px;
    font-size: 12px;
    color: #7d7d7d;
    font-weight: 400;
    display: flex;
    align-items: center;
    gap: 6px;
    position: relative;
    cursor: pointer;
    transition: color 0.3s;
    font-family: 'Poppins', sans-serif;

}

.tab-btn.active {
    color: #1C1C2E;
    font-weight: 600;
}

.tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: 0px;
    left: 3px;
    width: 100%;
    height: 2px;
    background-color: #1473E6;
    border-radius: 1px;
}

button:focus {
    outline: none;
}

button.create-new-list {
    margin: 8px 0 5px 0;
    border: 0;
    background-color: #115ea3;
    color: #fff;
    font-family: 'Poppins';
    padding: 4px 20px 3px 20px;
    border-radius: 20px;
    cursor: pointer;
    outline: none;
    font-family: 'Poppins', sans-serif;
    font-size: 14px;
}

button.create-new-list img {
    margin: 0 4px 2px 0px;
}

.custom-table-container {
    width: 100%;
    overflow-x: auto;
    padding: 5px 0 0 0;
    margin: 0;
}

.custom-table {
    width: 100%;
    border-collapse: collapse;
    /* table-layout: fixed;  */
    font-size: 14px;
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
}

.custom-table td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid #f0f0f0;
    font-size: 12px;
    color: #283547;
    font-family: 'Poppins', sans-serif;

}

.custom-table th {
    background-color: #ffffff;
    font-weight: 500;
    color: #666;
    padding: 15px 0 0 16px;
    border-bottom: 1px solid #f0f0f0;
    font-size: 12px;
    font-family: 'Poppins', sans-serif;

}

/* .actions-cell {
    display: flex;
    align-items: center;
    gap: 8px;
    justify-content: center;
} */

button.verify-btn {
    background-color: #fff;
    border: 1px solid #DDDDDD;
    color: #333;
    padding: 4px 12px;
    border-radius: 6px;
    font-weight: 400;
    cursor: pointer;
    font-size: 12px;
    font-family: 'Poppins', sans-serif;
    margin: 0 8px 0 0 ;

}

button.ready-btn {
    background-color: #115EA3;
    border: none;
    color: #fff;
    padding: 6px 20px;
    border-radius: 4px;
    font-weight: 400;
    cursor: pointer;
    font-size: 12px;
    font-family: 'Poppins', sans-serif;
    margin: 0 8px 0 0 ;


}

@keyframes slideLeftToRight {
    0% {
        transform: translateX(-100%);
        opacity: 0;
    }

    50% {
        opacity: 1;
    }

    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

button.verifying-btn {
    background-color: #F5F5F5;
    border: 1px solid #DDDDDD;
    color: #151417;
    padding: 6px 12px;
    border-radius: 6px;
    font-weight: 400;
    display: flex;
    align-items: center;
    gap: 6px;
    width: 150px;
    overflow: hidden;
}

button.verifying-btn span {
    display: inline-block;
    animation: slideLeftToRight 1.5s ease-out forwards;
}


.action-icon {
    cursor: pointer;
    color: #666;
    padding: 4px;
}

.delete-icon {
    color: #d9534f;
}

td.contact-list-plan {
    color: #151417;
    font-size: 12px;
}


/* .rotate-animation {
    animation: spin 2s linear infinite;
    display: inline-block;
    transform-origin: center center;
} */

@keyframes spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}





.verifying-btn-please {
    background-color: #F5F5F5;
    border: 1px solid #DDDDDD;
    color: #151417;
    padding: 8px 12px;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    width: 150px;
    margin: 0 8px 0 0;
}

.hover-button-wrapper {
    display: inline-block;
    position: relative;
}

.popup-fade {
    position: absolute;
    top: 110%;
    left: -20%;
    transform: translateX(-50%);
    background: #333;
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    white-space: nowrap;
    opacity: 0;
    animation: fadeIn 0.5s forwards;
    z-index: 1000;
}

@keyframes fadeIn {
    to {
        opacity: 1;
    }
}


.custom-tab-header {
    display: flex;
    border-bottom: 1px solid #ddd;
    /* Optional overall border */
}

.custom-tab-button {
    background: none;
    border: none;
    outline: none;
    padding: 6px 12px;
    font-size: 14px;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    display: flex;
    align-items: center;
    color: #555;
    transition: border-bottom 0.5s, color 0.5s;
}

.custom-tab-button img {
    vertical-align: middle;
}

.custom-tab-button.is-active {
    border-bottom: 2px solid #1473E6;
    /* Or any color you want */
    color: #000;
    /* Optional: change text color */
    font-weight: 600;
}


