import { format } from 'date-fns';
import Mixpanel from "mixpanel-browser";
import { useContext, useEffect, useState } from "react";
import { useNavigate } from 'react-router-dom';
import Analytics from "../../utils/analyticsTracking";
import { PostWithTokenNoCache } from "../common-files/ApiCalls";
import { ApiName } from "../common-files/ApiNames";
import DashboardContext from "../common-files/ContextDashboard";
import { useMicrosoftLoginLogic } from "../common-files/SocialMediaAuth";
import UseTabStore from "../common-files/useGlobalState";

// Add a tracking function
const trackAccountAction = (eventName, properties = {}) => {
    try {
        // Get user data
        const userData = JSON.parse(localStorage.getItem("user")) || {};
        const userEmail = userData.email;
        const userRole = userData.role;

        // Get device info
        const deviceInfo = {
            device_type: /mobile|tablet|android/i.test(navigator.userAgent) ? 'Mobile' : 'Desktop',
            browser: navigator.userAgent,
            viewport_size: `${window.innerWidth}x${window.innerHeight}`,
            language: navigator.language || navigator.userLanguage
        };

        // Track in Analytics
        Analytics.track(eventName, {
            user_email: userEmail,
            user_role: userRole,
            current_url: window.location.href,
            timestamp: new Date().toISOString(),
            ...properties,
            ...deviceInfo
        });

        // Also identify in Mixpanel if we have user email
        if (userEmail) {
            Mixpanel.identify(userEmail);

            // Set user properties based on event
            if (eventName === "Profile Updated") {
                Mixpanel.people.set({
                    'Last Profile Update': new Date().toISOString(),
                    'Profile Completeness': properties.fields_updated ? `${Object.keys(properties.fields_updated).length} fields` : 'Unknown'
                });
            }
        }
    } catch (error) {
        console.warn('Error tracking account action:', error);
    }
};

const SubscriptionTab = ({ userCredits }) => {
    const navigate = useNavigate();
    const { Microsoftlogout } = useMicrosoftLoginLogic();
    const [deactivateProfile, setDeactivateProfile] = useState(false);
    // State for transaction history
    const [transactionHistory, setTransactionHistory] = useState([]);
    const [isLoadingTransactions, setIsLoadingTransactions] = useState(false);
    const [transactionError, setTransactionError] = useState("");
    const [isPopupOpen, setIsPopupOpen] = useState(false);
    const userMembership = localStorage.getItem("userMembership") || null;
    const { dataDC } = useContext(DashboardContext);
    // Fetch transaction history
    const {
        setButtonType,
        setDefaultAlert,
        setDefaultErrorMsg,
        setSelectedCompanyAddressCountry,
        setSelectedCompanyAddressState,
        setSelectedCompanyAddressCity,
        setSelectedCompanyZipCode,
        setSelectedContactJobTitle1,
        setSelectedContactJobTitleLevel1,
        setSelectedContactJobDeptName1,
        setSelectedContactJobFunctionName1,
        setCheckedItems,
        setCompanyTypeCheckedBoxes,
        setSelectedCompanyEmployeeSize,
        setSelectedCompanyAnnualRevenueAmount,
        setSelectedCompanyTechKeyWordsList,
        setSelectedCompany,
        setSortingBy,
        setDefaultError,
        setNumber,
        setSearchPattern,
        setWithDownloaded,
        setResetPage,
        setCheckboxesGlobal,
        setSelectedData,
        setSelectedRows,
        setAllRecordsNew,
        setSampleDownloadedIds,
        setLoadedData,
        setViewModal,
        setViewSelected,
        setSelectedValues,
        setCheckedBoxes,
        setSelectedOptions,
        setSicCode,
        setIndustryData,
        setShowDownloadedData,
        setSelectedCompanyType,
        setSelectedCompanyURL
    } = UseTabStore();

    const clearSessionStorage = async () => {
        return new Promise((resolve) => {
            sessionStorage.clear();
            resolve();
        });
    };

    const fetchTransactionHistory = async () => {
        setIsLoadingTransactions(true);
        setTransactionError("");

        const params = {
            page: 1,
            pageSize: 7,
            sortBy: "desc",
            searchParams: {
                transaction_status: "paid"
            }
        };

        try {
            setIsLoadingTransactions(true);
            setTransactionError("");

            const response = await PostWithTokenNoCache(ApiName.transactionHistory, params);

            if (response && response.data) {
                // Check if response has status field, if not assume success (200 status from HTTP)
                const hasStatusField = 'status' in response.data;
                const isSuccess = hasStatusField ? response.data.status === 200 : response.status === 200;

                if (isSuccess) {
                    // Try response.data.data first (nested data), then response.data directly
                    let rawTransactionData = response.data.data;

                    // If response.data.data doesn't exist or is null, use response.data directly
                    if (!rawTransactionData) {
                        rawTransactionData = response.data;
                    }

                    // Handle JSON string response - parse if data is a string
                    if (typeof rawTransactionData === 'string') {
                        try {
                            rawTransactionData = JSON.parse(rawTransactionData);
                        } catch (parseError) {
                            console.error("Failed to parse transaction data JSON:", parseError);
                            setTransactionError("Failed to parse transaction data");
                            setIsLoadingTransactions(false);
                            return;
                        }
                    }

                    // Handle different possible data structures
                    let transactionData = [];

                    if (Array.isArray(rawTransactionData)) {
                        // Data is already an array
                        transactionData = rawTransactionData;
                    } else if (rawTransactionData && typeof rawTransactionData === 'object') {

                        if (Array.isArray(rawTransactionData.transactions)) {
                            transactionData = rawTransactionData.transactions;
                        } else if (Array.isArray(rawTransactionData.data)) {
                            transactionData = rawTransactionData.data;
                        } else if (Array.isArray(rawTransactionData.items)) {
                            transactionData = rawTransactionData.items;
                        } else if (Array.isArray(rawTransactionData.records)) {
                            transactionData = rawTransactionData.records;
                        } else if (Array.isArray(rawTransactionData.results)) {
                            transactionData = rawTransactionData.results;
                        } else if (Array.isArray(rawTransactionData.list)) {
                            transactionData = rawTransactionData.list;
                        } else {
                            // Check if any property contains an array
                            const arrayProperty = Object.keys(rawTransactionData).find(key =>
                                Array.isArray(rawTransactionData[key])
                            );

                            if (arrayProperty) {
                                transactionData = rawTransactionData[arrayProperty];
                            } else {
                                // Single transaction object, wrap in array
                                transactionData = [rawTransactionData];
                            }
                        }
                    } else {
                        if (rawTransactionData && rawTransactionData.items && Array.isArray(rawTransactionData.items)) {
                            transactionData = rawTransactionData.items;
                        } else {
                            transactionData = [];
                        }
                    }
                    const transformedTransactions = Array.isArray(transactionData) ? transactionData.map((apiTransaction) => {
                        const transformedTransaction = {
                            invoice_date: apiTransaction.transaction_createdAt || apiTransaction.created_at || apiTransaction.date,
                            invoice_id: apiTransaction.transaction_id || apiTransaction.id || apiTransaction.invoice_id,
                            status: apiTransaction.transaction_status || apiTransaction.status,
                            amount: apiTransaction.transaction_amount || apiTransaction.amount
                        };

                        return transformedTransaction;
                    }) : [];

                    setTransactionHistory(transformedTransactions);
                    setTransactionError("");
                } else {
                    setTransactionError("Failed to load transaction history");
                }
            } else {
                setTransactionError("Failed to load transaction history");
            }
        } catch (error) {
            const message = error?.response?.data?.message || "Failed to fetch transaction history";
            setTransactionError(message);
        } finally {
            setIsLoadingTransactions(false);
        }
    };

    // Fetch transaction history on component mount
    useEffect(() => {
        fetchTransactionHistory();
    }, []);

    // Helper function to format credit values
    const formatCreditValue = (value) => {
        if (value === null || value === undefined) return "0";
        if (typeof value === "number") return value.toLocaleString("en-IN");
        if (typeof value === "string" && value.toLowerCase() === "unlimited")
            return "Unlimited";
        return value;
    };

    // Helper function to format transaction date
    const formatTransactionDate = (dateString) => {
        if (!dateString) return "N/A";
        try {
            const date = new Date(dateString);
            return format(date, 'M/d/yyyy');
        } catch (error) {
            return dateString;
        }
    };

    // Helper function to format amount
    const formatAmount = (amount) => {
        if (!amount) return "–";
        try {
            let numericAmount;

            if (typeof amount === 'string') {
                numericAmount = parseFloat(amount) / 100;
            } else if (typeof amount === 'number') {
                numericAmount = amount > 1000 ? amount / 100 : amount;
            } else {
                console.warn("Unknown amount format:", amount);
                return "–";
            }

            return `$${numericAmount.toFixed(2)}`;
        } catch (error) {
            console.warn("Error formatting amount:", amount, error);
            return "–";
        }
    };

    // Parse the plan name to make it more readable
    const parsePlanName = (planName) => {
        if (!planName) return "Free";
        return planName
            .split('_')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');
    };

    // Format the period end date
    const formatDate = (dateString) => {
        if (!dateString) return "N/A";
        try {
            return format(new Date(dateString), 'MMM d, yyyy');
        } catch {
            return dateString;
        }
    };

    // Determine if the plan is yearly based on package_name and billing_period
    const isYearlyPlan = () => {
        if (!userCredits || !userCredits.user_plan_name) return false;

        // Check if plan name contains "yearly" (case insensitive)
        const isYearlyInName = userCredits.user_plan_name.toLowerCase().includes('yearly');

        // If billing_period is available in userCredits, use that
        if (userCredits.billing_period) {
            return userCredits.billing_period.toLowerCase() === 'yearly';
        }

        // Fallback to checking the plan name
        return isYearlyInName;
    };

    const logout = () => {

        sessionStorage.removeItem("jobDepartments");
        sessionStorage.removeItem("jobFunction");

        dataDC.jobTitle = {}; // Set jobTitle to an empty object

        dataDC.jobLevel = {}; // Set jobTitle to an empty object

        dataDC.DeptFunc = {}; // Set jobTitle to an empty object

        if ("company_employee_size" in dataDC && dataDC.company_employee_size !== undefined) {
            dataDC.company_employee_size = []; // Set company_employee_size to an empty object
        }

        if ("sic_code" in dataDC && dataDC.sic_code !== undefined) {
            dataDC.sic_code = []; // Set sic_code to an empty object
        }

        if ("company_annual_revenue_amount" in dataDC && dataDC.company_annual_revenue_amount !== undefined) {
            dataDC.company_annual_revenue_amount = []; // Set company_annual_revenue_amount to an empty object
        }

        setCheckedItems({});
        setCheckedBoxes([]);
        setSelectedValues([]);

        setSelectedOptions(null);
        setSicCode([]);
        setIndustryData([]);

        setSelectedCompanyAddressCountry({});
        setSelectedCompanyAddressState({});
        setSelectedCompanyAddressCity({});
        setSelectedCompanyZipCode({});
        setSelectedContactJobTitle1({});
        setSelectedContactJobTitleLevel1({});
        setSelectedContactJobDeptName1({});
        setSelectedContactJobFunctionName1({});
        setSelectedCompanyEmployeeSize({});
        setSelectedCompanyAnnualRevenueAmount({});
        setSelectedCompanyTechKeyWordsList({});
        setSelectedCompany({});
        setSortingBy({});
        setDefaultError({});
        setNumber({});
        setSearchPattern({});
        setCheckboxesGlobal([]);
        setSelectedData([]);
        setAllRecordsNew('');
        setSelectedRows([]);
        setSampleDownloadedIds('');
        setLoadedData([]);
        setViewModal(false);
        setViewSelected(false);
        setViewModal(false);
        setLoadedData([]);
        setResetPage(true);
        setShowDownloadedData(true);
        setWithDownloaded(true);
        setCompanyTypeCheckedBoxes([]);
        setSelectedCompanyType({});
        setSelectedCompanyURL({});

        if (localStorage.getItem("signin-type") == "microsoft") {
            Microsoftlogout();
        }
        PostWithTokenNoCache(ApiName.deviceLogout, {}).then((res) => {
            if (res && "status" in res) {
                if (res.data.status == 200) {
                    clearSessionStorage();
                    navigate("/");
                }
            }
        });
    };

    const deactivateAccount = (e) => {
        e.preventDefault();

        // Track deactivation attempt
        trackAccountAction("Account Deactivation", {
            action: "initiated",
            status: "modal_opened"
        });

        setDeactivateProfile(true);
        setIsPopupOpen(true);
    };

    const onCloseDeactivate = () => {
        // Track deactivation cancellation
        trackAccountAction("Account Deactivation", {
            action: "cancelled",
            status: "modal_closed"
        });

        setIsPopupOpen(false);
    };

    const onDeactivateSuccess = async () => {
        setIsPopupOpen(false);

        // Track deactivation confirmation
        trackAccountAction("Account Deactivation", {
            action: "confirmed",
            status: "in_progress"
        });

        let params = {
            method: "POST",
        };
        try {
            let url = userMembership === "prime"
                ? ApiName.cancelSubscriptionAccount
                : ApiName.deactivateUserAccount;

            const res = await PostWithTokenNoCache(url, params);
            if (res?.status === 200) {
                // Track successful deactivation before logout
                trackAccountAction("Account Deactivation", {
                    action: "completed",
                    status: "success"
                });

                logout();
            }
        } catch (error) {
            setButtonType("error");
            setDefaultErrorMsg(error?.response?.data?.message);
            setDefaultAlert(true);

            // Track failed deactivation
            trackAccountAction("Account Deactivation", {
                action: "failed",
                status: "error",
                error_message: error?.response?.data?.message || "Unknown error"
            });
        }
    };

    return (
        <div className="profile-box-2">
            <div className="row">
                <div className="col-md-2"></div>
                <div className="col-md-8">
                    <div className="profile-box">
                        <div className="my-sub">
                            <div className="d-flex flex-row justify-content-between">
                                <div>
                                    <p className="my-second-subscription">Subscription</p>
                                </div>
                                <div>
                                    <div className="d-flex gap-2">
                                        <button type="button" className="my-upgrade">Upgrade</button>
                                    </div>
                                </div>
                            </div>

                            <div className="sub-table-border">
                                <div className="current-plan">
                                    <p className="my-current plan">Current Plan</p>
                                </div>
                                <div className="row">
                                    <div className="col-md-6">
                                        <p className="my-freemium">
                                            {userCredits ? parsePlanName(userCredits.user_plan_name) : "Loading..."}
                                        </p>
                                    </div>
                                    <div className="col-md-6">
                                        <p className="check-my-views">
                                            <img src="images/account-credits-view-icon.png" alt="Views" />
                                            {userCredits
                                                ? userCredits.total_assigned_contact_view === "Unlimited"
                                                    ? <span>&nbsp;Unlimited Profile Views</span>
                                                    : `${formatCreditValue(userCredits.total_balance_contact_view)}/${formatCreditValue(userCredits.total_assigned_contact_view)} Profile Views`
                                                : "Loading..."}
                                        </p>
                                    </div>
                                </div>
                                <div className="row">
                                    <div className="col-md-6">
                                        <p className="my-freemium">
                                            {userCredits?.period_end
                                                ? `Renews on ${formatDate(userCredits.period_end)}`
                                                : "Loading..."}
                                        </p>
                                    </div>
                                    <div className="col-md-6">
                                        <p className="check-my-views">
                                            <img src="images/accounts-credits-download.png" alt="Downloads" />
                                            {userCredits
                                                ? `${formatCreditValue(userCredits.total_assigned_credit)} ${isYearlyPlan() ? 'Yearly' : 'Monthly'} Downloads`
                                                : "Loading..."}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="col-md-2"></div>
            </div>

            <div className="row">
                <div className="col-md-2"></div>
                <div className="col-md-8">
                    <div className="profile-box mt-3">
                        <div className="my-sub">
                            <div style={{ marginBottom: 12, fontWeight: 600, fontSize: 16 }}>
                                Invoice History
                            </div>
                            <div className="invoice-container">
                                <div className="invoice-vertical-scroll">
                                    <table className="invoice-table">
                                        <thead>
                                            <tr>
                                                <th>Invoice Date</th>
                                                <th>Invoice ID</th>
                                                <th>Status</th>
                                                <th>Amount</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {isLoadingTransactions ? (
                                                <tr>
                                                    <td colSpan="4" style={{ textAlign: 'center', padding: '20px' }}>
                                                        Loading transaction history...
                                                    </td>
                                                </tr>
                                            ) : transactionError ? (
                                                <tr>
                                                    <td colSpan="4" style={{ textAlign: 'center', padding: '20px', color: 'red' }}>
                                                        {transactionError}
                                                    </td>
                                                </tr>
                                            ) : transactionHistory.length === 0 ? (
                                                <tr>
                                                    <td colSpan="4" style={{ textAlign: 'center', padding: '20px' }}>
                                                        No transaction history found
                                                    </td>
                                                </tr>
                                            ) : (
                                                transactionHistory.map((transaction, idx) => (
                                                    <tr key={transaction.invoice_id || idx}>
                                                        <td>{formatTransactionDate(transaction.invoice_date)}</td>
                                                        <td>{transaction.invoice_id || 'N/A'}</td>
                                                        <td className="status paid">{transaction.status || 'Paid'}</td>
                                                        <td>{formatAmount(transaction.amount)}</td>
                                                    </tr>
                                                ))
                                            )}
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
                <div className="col-md-2">
                    <div>
                        <button
                            type="button"
                            className="deactivate"
                            onClick={deactivateAccount}
                        >
                            Cancel Subscription
                        </button>
                    </div>
                </div>
            </div>
            {isPopupOpen && (
                <Popup
                    onCloseDeactivate={onCloseDeactivate}
                    onDeactivateSuccess={onDeactivateSuccess}
                />
            )}
        </div>
    );
};

export default SubscriptionTab;

const Popup = ({ onCloseDeactivate, onDeactivateSuccess }) => {
    return (
        <div
            class="modal"
            style={{
                display: "block",
                important: "true",
                backgroundColor: "#0000007d",
            }}
            id="exampleModalCenter"
            tabindex="-1"
            role="dialog"
            aria-labelledby="exampleModalCenterTitle"
        >
            <div class="modal-dialog modal-dialog-centered" role="document">
                <div class="modal-content">
                    <div
                        class="modal-header"
                        style={{ borderBottom: "0", padding: "6px 11px 0 0px" }}
                    >
                        <h5 class="modal-title" id="exampleModalLongTitle"></h5>
                        <button
                            type="button"
                            class="close"
                            data-dismiss="modal"
                            onClick={onCloseDeactivate}
                            aria-label="Close"
                        >
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body" style={{ borderBottom: "0", padding: "0" }}>
                        <p className="areyousure">
                            Are you sure you want to cancel<br />your subscription?
                        </p>

                        <div className="d-flex flex-row justify-content-center">
                            <div className="mr-5">
                                <button
                                    type="submit"
                                    onClick={onDeactivateSuccess}
                                    className="yesno"
                                >
                                    Yes
                                </button>
                            </div>
                            <div>
                                <button
                                    type="submit"
                                    onClick={onCloseDeactivate}
                                    className="no"
                                >
                                    No
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};