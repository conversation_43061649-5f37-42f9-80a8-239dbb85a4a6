import React, { useEffect, useState } from 'react';
import AsyncSelect from 'react-select/async';
import { PostWithTokenNoCache } from '../common-files/ApiCalls.js';
import { ApiName } from '../common-files/ApiNames.js';
import { SearchJobtitleStyles } from "../common-files/FiltersData.js";
import UseTabStore from '../common-files/useGlobalState.js';


const Location = () => {
    // State to store selected values
    const {
        companyAddressCountry,
        companyAddressState,
        companyAddressCity,
        companyZipCode,
        setSelectedCompanyAddressCountry,
        setSelectedCompanyAddressState,
        setSelectedCompanyAddressCity,
        setSelectedCompanyZipCode,
        setIsFiltered
    } = UseTabStore();
    const [inputValue, setInputValue] = useState('');
    const [isCountryAutoFocus, setIsCountryAutoFocus] = useState(false); // Set one of them to autofocus initially
    const [isStateAutoFocus, setIsStateAutoFocus] = useState(false);
    const [isCityAutoFocus, setIsCityAutoFocus] = useState(true);
    const [isZipCodeAutoFocus, setIsZipCodeAutoFocus] = useState(false);

    const [selectedCityValues, setSelectedCityValues] = useState([]);
    const [selectedStateValues, setSelectedStateValues] = useState([]);
    const [selectedCountryValues, setSelectedCountryValues] = useState([]);
    const [selectedZipCodeValues, setSelectedZipCodeValues] = useState([]);

    useEffect(() => {
        if (Object.keys(companyAddressCity).length < 1) {
            setSelectedCityValues([]);
        } else {
            const selectedValues = Object.keys(companyAddressCity).map(key => {
                return { label: companyAddressCity[key], value: companyAddressCity[key] };
            });
            setSelectedCityValues(selectedValues);
        }
    }, [companyAddressCity])

    useEffect(() => {
        if (Object.keys(companyAddressState).length < 1) {
            setSelectedStateValues([]);
        } else {
            const selectedValues = Object.keys(companyAddressState).map(key => {
                return { label: companyAddressState[key], value: companyAddressState[key] };
            });
            setSelectedStateValues(selectedValues);
        }
    }, [companyAddressState])

    useEffect(() => {
        if (Object.keys(companyAddressCountry).length < 1) {
            setSelectedCountryValues([]);
        } else {
            const selectedValues = Object.keys(companyAddressCountry).map(key => {
                return { label: companyAddressCountry[key], value: companyAddressCountry[key] };
            });
            setSelectedCountryValues(selectedValues);
        }
    }, [companyAddressCountry])

    useEffect(() => {
        if (Object.keys(companyZipCode).length < 1) {
            setSelectedZipCodeValues([]);
        } else {
            const selectedValues = Object.keys(companyZipCode).map(key => {
                return { label: companyZipCode[key], value: companyZipCode[key] };
            });
            setSelectedZipCodeValues(selectedValues);
        }
    }, [companyZipCode])

    const noOptionsMessage = () => {
        return inputValue.length < 2 ? 'No options found' : 'No options found';
    };

    const convertToProperCase = async (data) => {
        let properCaseData = [];
        if (data) {
            properCaseData = data.map(val => {
                // Trim the input string to remove leading and trailing whitespace
                val = val.trim();

                // Match words while preserving non-alphanumeric characters in between
                const words = val.match(/\b\w+\b/g);
                let result = "";
                let lastIndex = 0;

                words.forEach(word => {
                    // Find the position of each word in the original string
                    const start = val.indexOf(word, lastIndex);

                    // Append special characters or spaces before the current word
                    result += val.slice(lastIndex, start);

                    // Capitalize the word
                    result += word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();

                    // Update lastIndex to the end of this word
                    lastIndex = start + word.length;
                });

                // Append any remaining special characters or spaces after the last word
                result += val.slice(lastIndex);

                return result;
            });
        }
        return properCaseData;
    };



    const SearchCity = ({ autoFocus, onFocus, onBlur }) => {

        // Handle value change when an option is selected or deselected
        const handleValueChange = (selectedOptions) => {
            setIsFiltered(true);

            setSelectedCityValues(selectedOptions);
            let updatedCity = {};

            // Update the company names based on selected options
            selectedOptions.forEach((item, index) => {
                updatedCity[index] = item.value;
            });

            setSelectedCompanyAddressCity(updatedCity);
        };

        const loadOptions = (inputText, callback) => {
            if (inputText.length < 1) {
                callback([]);
                return;
            }
            if (inputText && inputText.length > 0) {
                const dataPost = {
                    company_address_city: inputText // Pass the inputValue to your API request
                };
                // Make an API call here to fetch suggestions based on the inputValue
                PostWithTokenNoCache(ApiName.esAutoSuggestions, dataPost)
                    .then(async (res) => {
                        if (res.data.status === 200) {
                            const city = JSON.parse(res.data.data);

                            let properCaseCities = await convertToProperCase(city);

                            // Convert the array to a Set to remove duplicates and then convert it back to an array
                            const uniqueCity = [...new Set(properCaseCities)];

                            const options = uniqueCity.map((option) => ({
                                label: option,
                                value: option
                            }));
                            callback(options);
                        }
                    })
                    .catch((error) => {
                        console.error('Error fetching data:', error);
                        callback([]); // Clear options on error
                    });
            }
        };

        return (
            <div className="mb-2 mt-1 d-flex flex-column">
                <div className="">
                    <AsyncSelect
                        cacheOptions
                        defaultOptions
                        loadOptions={loadOptions}
                        isMulti
                        placeholder="Search by City"
                        onChange={handleValueChange} // Handle selected value changes
                        value={selectedCityValues} // Pass selected values
                        styles={SearchJobtitleStyles}
                        autoFocus={autoFocus}
                        onFocus={onFocus}
                        onBlur={onBlur}
                        isClearable
                        noOptionsMessage={noOptionsMessage}
                    />
                </div>
                <div className="search-icon">
                    <i className="fas fa-search"></i>
                </div>

            </div>
        )
    }

   
	const SearchJobtitleStyles = {
		control: (provided, state) => ({
			...provided,
			marginLeft: "16px",
			backgroundColor: "#fff",
			border: "1px solid #093D54",
			cursor: "text",
			color: "#000",
			":hover": {
				borderColor: "#000",
			},
		}),

		placeholder: (provided) => ({
			...provided,
			whiteSpace: "nowrap",        // Prevent line break
			overflow: "hidden",          // Hide overflow
			textOverflow: "ellipsis",    // Add "..." if too long
			fontSize: "13px",
		  }),

		singleValue: (provided) => ({
			...provided,
			borderRadius: "10px",
			backgroundColor: "#fff",
			color: "#000",
		}),

		multiValue: (provided) => ({
			...provided,
			borderRadius: "10px",
			backgroundColor: "white",
			marginTop: "8px",
			border: "1px solid",
			fontSize: "14px",
			color: "#000",
		}),

		multiValueRemove: (base) => ({
			...base,
			backgroundColor: "#fff",
			color: "#000",
			borderRadius: "10px",
			border: "1px solid #08CEDC",
			marginTop: "-7px",
			marginBottom: "2px",
			width: "11px",
			height: "11px",
			":hover": {
				backgroundColor: "#55C2C3",
				color: "black",
			},
		}),

		menu: (provided) => ({
			...provided,
			backgroundColor: "#fff",
			color: "#000",
			border: "1px solid #093D54",
			position: "absolute",       // Changed from relative
			width: "220px",
			left: "16px",                    // Aligns to the left of the input
			zIndex: 9999,
			fontSize:"14px"
		}),


		menuList: (provided) => ({
			...provided,
			backgroundColor: "#fff",
			color: "#000",
		}),

		option: (provided, state) => ({
			...provided,
			backgroundColor: state.isFocused ? "#f0f0f0" : "#fff",
			color: "#000",
			cursor: "pointer",
		}),

		valueContainer: (provided) => ({
			...provided,
			padding: "0px 5px 0 0",
			width: "300px",
			color: "#000",
		}),

		container: (provided) => ({
			...provided,
			padding: "0px 11px 0 0",
		}),

		group: (provided) => ({
			...provided,
			width: "262px",
			backgroundColor: "#fff",
			color: "#000",
			border: "1px solid #093D54",
		}),

		indicatorsContainer: (provided, state) => ({
			...provided,
			visibility: "hidden",
			fontSize: "0",
		}),

	};

    const SearchState = ({ autoFocus, onFocus, onBlur }) => {

        // Handle value change when an option is selected or deselected
        const handleValueChange = (selectedOptions) => {
            setIsFiltered(true);

            setSelectedStateValues(selectedOptions);
            let updatedState = {};

            // Update the company names based on selected options
            selectedOptions.forEach((item, index) => {
                updatedState[index] = item.value;
            });

            setSelectedCompanyAddressState(updatedState);
        };

        const loadOptions = (inputText, callback) => {
            if (inputText.length < 1) {
                callback([]);
                return;
            }
            if (inputText && inputText.length > 0) {
                const dataPost = {
                    company_address_state: inputText // Pass the inputValue to your API request
                };
                // Make an API call here to fetch suggestions based on the inputValue
                PostWithTokenNoCache(ApiName.esAutoSuggestions, dataPost)
                    .then(async (res) => {
                        if (res.data.status === 200) {
                            const state = JSON.parse(res.data.data);

                            let properCaseStates = await convertToProperCase(state);

                            // Convert the array to a Set to remove duplicates and then convert it back to an array
                            const uniqueState = [...new Set(properCaseStates)];

                            const options = uniqueState.map((option) => ({
                                label: option,
                                value: option
                            }));
                            callback(options);
                        }
                    })
                    .catch((error) => {
                        console.error('Error fetching data:', error);
                        callback([]); // Clear options on error
                    });
            }
        };

        return (
            <div className="mb-2 mt-1 d-flex flex-column">
                <div className="">
                    <AsyncSelect
                        cacheOptions
                        defaultOptions
                        loadOptions={loadOptions}
                        isMulti
                        placeholder="Search by State"
                        onChange={handleValueChange} // Handle selected value changes
                        value={selectedStateValues} // Pass selected values
                        styles={SearchJobtitleStyles}
                        autoFocus={autoFocus}
                        onFocus={onFocus}
                        onBlur={onBlur}
                        isClearable
                        noOptionsMessage={noOptionsMessage}
                    />
                </div>
                <div className="search-icon">
                    <i className="fas fa-search"></i>
                </div>

            </div>
        )
    }

    const SearchCountry = ({ autoFocus, onFocus, onBlur }) => {

        // Handle value change when an option is selected or deselected
        const handleValueChange = (selectedOptions) => {
            setIsFiltered(true);

            setSelectedCountryValues(selectedOptions);
            let updatedCountry = {};

            // Update the company names based on selected options
            selectedOptions.forEach((item, index) => {
                updatedCountry[index] = item.value;
            });

            setSelectedCompanyAddressCountry(updatedCountry);
        };

        const loadOptions = (inputText, callback) => {
            if (inputText.length < 1) {
                callback([]);
                return;
            }
            if (inputText && inputText.length > 0) {
                const dataPost = {
                    company_address_country: inputText // Pass the inputValue to your API request
                };
                // Make an API call here to fetch suggestions based on the inputValue
                PostWithTokenNoCache(ApiName.esAutoSuggestions, dataPost)
                    .then(async (res) => {
                        if (res.data.status === 200) {
                            const country = JSON.parse(res.data.data);

                            let properCaseCountries = await convertToProperCase(country);

                            // Convert the array to a Set to remove duplicates and then convert it back to an array
                            const uniqueCountry = [...new Set(properCaseCountries)];

                            const options = uniqueCountry.map((option) => ({
                                label: option,
                                value: option
                            }));
                            callback(options);
                        }
                    })
                    .catch((error) => {
                        console.error('Error fetching data:', error);
                        callback([]); // Clear options on error
                    });
            }
        };

        return (
            <div className="mb-2 mt-1 d-flex flex-column">
                <div className="">
                    <AsyncSelect
                        cacheOptions
                        defaultOptions={false}
                        loadOptions={loadOptions}
                        isMulti
                        placeholder="Search by Country"
                        onChange={handleValueChange} // Handle selected value changes
                        value={selectedCountryValues} // Pass selected values
                        styles={SearchJobtitleStyles}
                        autoFocus={autoFocus}
                        onFocus={onFocus}
                        onBlur={onBlur}
                        isClearable
                        noOptionsMessage={noOptionsMessage}
                    />
                </div>
                <div className="search-icon">
                    <i className="fas fa-search"></i>
                </div>

            </div>
        )
    }

    const SearchZipCode = ({ autoFocus, onFocus, onBlur }) => {

        // Handle value change when an option is selected or deselected
        const handleValueChange = (selectedOptions) => {
            setIsFiltered(true);

            setSelectedZipCodeValues(selectedOptions);
            let updatedZipCode = {};

            // Update the company names based on selected options
            selectedOptions.forEach((item, index) => {
                updatedZipCode[index] = item.value;
            });

            setSelectedCompanyZipCode(updatedZipCode);
        };

        const loadOptions = (inputText, callback) => {
            if (inputText.length < 1) {
                callback([]);
                return;
            }
            if (inputText && inputText.length > 0) {
                const dataPost = {
                    company_address_zipcode: inputText // Pass the inputValue to your API request
                };
                // Make an API call here to fetch suggestions based on the inputValue
                PostWithTokenNoCache(ApiName.esAutoSuggestions, dataPost)
                    .then((res) => {
                        if (res.data.status === 200) {
                            const zipcode = JSON.parse(res.data.data);
                            // Convert the array to a Set to remove duplicates and then convert it back to an array
                            const uniqueZipcode = [...new Set(zipcode)];

                            const options = uniqueZipcode.map((option) => ({
                                label: option,
                                value: option
                            }));
                            callback(options);
                        }
                    })
                    .catch((error) => {
                        console.error('Error fetching data:', error);
                        callback([]); // Clear options on error
                    });
            }
        };

        return (
            <div className="mb-2 mt-1 d-flex flex-column">
                <div className="">
                    <AsyncSelect
                        cacheOptions
                        defaultOptions
                        loadOptions={loadOptions}
                        isMulti
                        placeholder="Search by Zip code"
                        onChange={handleValueChange} // Handle selected value changes
                        value={selectedZipCodeValues} // Pass selected values
                        styles={SearchJobtitleStyles}
                        autoFocus={autoFocus}
                        onFocus={onFocus}
                        onBlur={onBlur}
                        isClearable
                        noOptionsMessage={noOptionsMessage}
                    />
                </div>
                <div className="search-icon">
                    <i className="fas fa-search"></i>
                </div>

            </div>
        )
    }

    return (
        <div className="locks">
            <label className="requiredalue  mt-3">City</label>
            <SearchCity
                autoFocus={isCityAutoFocus}
                onFocus={() => setIsCityAutoFocus(true)}
                onBlur={() => setIsCityAutoFocus(false)}
            />
            <label className="requiredalue">State</label>
            <SearchState
                autoFocus={isStateAutoFocus}
                onFocus={() => setIsStateAutoFocus(true)}
                onBlur={() => setIsStateAutoFocus(false)}
            />
            <label className="requiredalue">Country</label>
            <SearchCountry
                autoFocus={isCountryAutoFocus}
                onFocus={() => setIsCountryAutoFocus(true)}
                onBlur={() => setIsCountryAutoFocus(false)}
            />
            <label className="requiredalue">Zip Code</label>
            <SearchZipCode
                autoFocus={isZipCodeAutoFocus}
                onFocus={() => setIsZipCodeAutoFocus(true)}
                onBlur={() => setIsZipCodeAutoFocus(false)}
            />

        </div>
    )


}
export default Location;