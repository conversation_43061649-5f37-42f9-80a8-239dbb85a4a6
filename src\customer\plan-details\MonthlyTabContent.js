import React, { useEffect, useState } from "react";
import UseTabStore from "../common-files/useGlobalState";
import { useNavigate } from "react-router-dom";
import { envConfig } from "../../config";
import Popup from "./Popup";
import Analytics from "../../utils/analyticsTracking.js";

const MonthlyTabContent = ({ isOpen, togglePopup }) => {

    const { allPlanDetails, setSelectedPlanDetails, setSelectedPlanType, setButtonType, setDefaultAlert, setDefaultErrorMsg } = UseTabStore();
    const [filteredPlans, setFilteredPlans] = useState([]);
    const navigate = useNavigate();
    const [user, setUser] = useState(JSON.parse(localStorage.getItem('user')));

    let selected_plan_name;
    let userCredits = localStorage.getItem("userCredits");
    if (userCredits) {
        const parsedData = JSON.parse(userCredits);
        selected_plan_name = parsedData.user_plan_name || null;
    }

    useEffect(() => {
        const desiredIds = envConfig.monthlyPlanIds;
        if (allPlanDetails && allPlanDetails.length > 0) {
            const specificPlans = allPlanDetails.filter((plan) => desiredIds.includes(plan.id));
            setFilteredPlans(specificPlans);
            
            // Track pricing page visit
            Analytics.trackFeatureUsage('Pricing Page View', { 
                user_email: user?.email || 'unknown',
                tab: 'monthly',
                plan_count: specificPlans.length,
                current_plan: selected_plan_name || 'none'
            });
        }
    }, [allPlanDetails]);

    const closeModal = () => {
        const modal = document.getElementById('pricePlanModal');
        if (modal) {
            modal.classList.remove('show');
            modal.style.display = 'none';
        }

        const backdrop = document.querySelector('.modal-backdrop');
        if (backdrop) {
            backdrop.remove();
        }

        document.body.classList.remove('modal-open');
        document.body.style.overflow = '';
        setButtonType("");
        setDefaultAlert(false);
        setDefaultErrorMsg("");
        
        // Track modal close
        Analytics.trackFeatureUsage('Pricing Modal Close', { 
            user_email: user?.email || 'unknown',
            tab: 'monthly'
        });
    };

    const upgradePlan = (selectedPlan) => {
        closeModal();
        setSelectedPlanType("upgrade");
        setSelectedPlanDetails(selectedPlan);
        
        // Track plan selection
        Analytics.trackFeatureUsage('Plan Selected', { 
            user_email: user?.email || 'unknown',
            plan_name: selectedPlan.package_name || 'unknown',
            plan_id: selectedPlan.id,
            plan_price: selectedPlan.price,
            billing_cycle: 'monthly',
            current_plan: selected_plan_name || 'none'
        });
        
        navigate('/upgrade-plan', {
            state: {
                email: user.email,
                token: user.token,
                planDetails: selectedPlan
            }
        });
    };
    
    // Track popup toggle for Contact Sales
    const handleTogglePopup = () => {
        Analytics.trackFeatureUsage('Contact Sales Modal', { 
            user_email: user?.email || 'unknown',
            action: isOpen ? 'close' : 'open',
            from_tab: 'monthly',
            current_plan: selected_plan_name || 'none'
        });
        togglePopup();
    };

    return (
        <div>
            {filteredPlans && filteredPlans.length > 0 &&
                <div className="container">
                    {/* Monthly Pricing Table Content */}
                    <div className="row">
                        <div className="col p-0">
                            <div className="top-penguin">
                                <img src="../images/top-penguin.png" width="220" alt="top-penguin" />
                            </div>
                        </div>
                        {filteredPlans.map((plan) => (
                            (envConfig.environment === "production" ? plan.id === 6688324 : plan.id === 329894) && (
                                <div class="col  p-0">
                                    <div class="pricing-card-1 position-relative">
                                        <div className="glaciar-parts">
                                            <img src="../images/glaciar.png" alt="glaciar" />
                                        </div>
                                        <h4 className="Glacier-of-month">Glacier</h4>
                                        <span class="current-price-2">${plan?.price ? plan?.price : ""}</span>
                                        <span class="per-month">/Month</span>

                                        <p class="text-muted-italic">Billed Monthly</p>
                                        {selected_plan_name === plan.package_name ? (
                                            <button class="btn-block-current-plan"><i className="fa fa-check checked-plan"></i> Current Plan</button>
                                        ) : (
                                            <button className="btn-block" onClick={() => upgradePlan(plan)}>Select</button>
                                        )}
                                        <p class="text-success">Switch to annual—save $120</p>


                                        <div class="features">
                                            <p>Unlimited</p>
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <p>{plan?.no_of_contact_views ? plan?.no_of_contact_views : ""}</p>
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <p>Unlimited</p>
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <p>5000/Month</p>
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <img alt="checks" src="../images/checks.png" />
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <img alt="checks" src="../images/checks.png" />
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <img alt="checks" src="../images/checks.png" />
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <img alt="checks" src="../images/checks.png" />
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <img alt="checks" src="../images/checks.png" />
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <img alt="checks" src="../images/checks.png" />
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <p>{plan?.no_of_users ? plan?.no_of_users : ""}</p>
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features-cross">
                                            <img alt="checks" src="../images/cancell.png" />
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <p>Freelancers & <br /> Solopreneurs</p>
                                        </div>
                                    </div>
                                </div>
                            )
                        ))}
                        {filteredPlans.map((plan) => (
                            (envConfig.environment === "production" ? plan.id === 6688310 : plan.id === 330220) && (
                                <div class="col  p-0">
                                    <div class="pricing-card-2 position-relative">
                                        <div class="recommended-badge">Recommended</div>
                                        <div className="ice-flow-img">
                                            <img alt="ice-floe" src="../images/ice-floe.png" />
                                        </div>
                                        <h4 className="ice-floe-top">Ice Floe</h4>
                                        <span class="current-price-2">${plan?.price ? plan?.price : ""}</span>
                                        <span class="per-month">/Month</span>

                                        <p class="text-muted-italic">Billed Monthly</p>
                                        {selected_plan_name === plan.package_name ? (
                                            <button class="btn-block-current-plan"><i className="fa fa-check checked-plan"></i> Current Plan</button>
                                        ) : (
                                            <button className="btn-block" onClick={() => upgradePlan(plan)}>Select</button>
                                        )}
                                        <p class="Unlock-annual">Switch to annual—save $120</p>

                                        <div class="features">
                                            <p>Unlimited</p>
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <p>{plan?.no_of_contact_views ? plan?.no_of_contact_views : ""}</p>
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <p>Unlimited</p>
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <p>10,000/Month</p>
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <img alt="checks" src="../images/checks.png" />
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <img alt="checks" src="../images/checks.png" />
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <img alt="checks" src="../images/checks.png" />
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <img alt="checks" src="../images/checks.png" />
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <img alt="checks" src="../images/checks.png" />
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <img alt="checks" src="../images/checks.png" />
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <p>{plan?.no_of_users ? plan?.no_of_users : ""}</p>
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features-cross">
                                            <img alt="checks" src="../images/cancell.png" />
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <p>Growing Teams & <br /> Startups</p>
                                        </div>
                                    </div>
                                </div>
                            )
                        ))}
                        {filteredPlans.map((plan) => (
                            (envConfig.environment === "production" ? plan.id === 6688283 : plan.id === 330881) && (
                                <div class="col  p-0">
                                    <div class="pricing-card-3 position-relative">
                                        <div className="polar-peak-img">
                                            <img src="../images/ice-mountain.png" width="110" />
                                        </div>
                                        <h4 className="ice-floe">Polar Peak</h4>
                                        <span class="current-price-2">${plan?.price ? plan?.price : ""}</span>
                                        <span class="per-month">/Month</span>

                                        <p class="text-muted-italic">Billed Monthly</p>
                                        {selected_plan_name === plan.package_name ? (
                                            <button class="btn-block-current-plan"><i className="fa fa-check checked-plan"></i> Current Plan</button>
                                        ) : (
                                            <button className="btn-block" onClick={() => upgradePlan(plan)}>Select</button>
                                        )}
                                        <p class="unlock-success">Switch to annual—save $240</p>

                                        <div class="features">
                                            <p>Unlimited</p>
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <p>{plan?.no_of_contact_views ? plan?.no_of_contact_views : ""}</p>
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <p>Unlimited</p>
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <p>20,000/Month</p>
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <img alt="checks" src="../images/checks.png" />
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <img alt="checks" src="../images/checks.png" />
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <img alt="checks" src="../images/checks.png" />
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <img alt="checks" src="../images/checks.png" />
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <img alt="checks" src="../images/checks.png" />
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <img alt="checks" src="../images/checks.png" />
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <p>{plan?.no_of_users ? plan?.no_of_users : ""}</p>
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features-cross">
                                            <img alt="checks" src="../images/checks.png" />
                                        </div>
                                        <hr className="horizontal-hr-2" />
                                        <div class="features">
                                            <p> Mid-Size Businesses & <br /> Emerging Enterprises</p>
                                        </div>
                                    </div>
                                </div>
                            )
                        ))}
                        <div class="col  p-0">
                            <div class="pricing-card-4 position-relative">
                                <div className="frozen-fortune-img">
                                    <img src="../images/frozen-fortune.png" width="213" />
                                </div>
                                <h4 className="ice-floe">Frozen Fortune</h4>
                                <p class="Playing-it">Playing it big?</p>
                                <p class="text-Fully">Fully Customized For Any Team Size</p>
                                <button onClick={handleTogglePopup} class="btn-block">Contact Sales</button>

                                <p class="unlock-success-blank"></p>
                                <hr className="horizontal-hr-3" />

                                <div class="features">
                                    <p>Custom</p>
                                </div>
                                <hr className="horizontal-hr-2" />
                                <div class="features">
                                    <p>Custom</p>
                                </div>
                                <hr className="horizontal-hr-2" />
                                <div class="features">
                                    <p>Custom</p>
                                </div>
                                <hr className="horizontal-hr-2" />
                                <div class="features">
                                    <p>Custom</p>
                                </div>
                                <hr className="horizontal-hr-2" />
                                <div class="features">
                                    <img alt="checks" src="../images/checks.png" />
                                </div>
                                <hr className="horizontal-hr-2" />
                                <div class="features">
                                    <img alt="checks" src="../images/checks.png" />
                                </div>
                                <hr className="horizontal-hr-2" />
                                <div class="features">
                                    <img alt="checks" src="../images/checks.png" />
                                </div>
                                <hr className="horizontal-hr-2" />
                                <div class="features">
                                    <img alt="checks" src="../images/checks.png" />
                                </div>
                                <hr className="horizontal-hr-2" />
                                <div class="features">
                                    <img alt="checks" src="../images/checks.png" />
                                </div>
                                <hr className="horizontal-hr-2" />
                                <div class="features">
                                    <img alt="checks" src="../images/checks.png" />
                                </div>
                                <hr className="horizontal-hr-2" />
                                <div class="features">
                                    <p>Custom</p>
                                </div>
                                <hr className="horizontal-hr-2" />
                                <div class="features-cross">
                                    <img alt="checks" src="../images/checks.png" />
                                </div>
                                <hr className="horizontal-hr-2" />
                                <div class="features-Enterprises">
                                    <p>Enterprises & <br /> Large Teams</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }
            {isOpen && (
                <Popup togglePopup={togglePopup} />
            )}
        </div>
    )
};


export default MonthlyTabContent;