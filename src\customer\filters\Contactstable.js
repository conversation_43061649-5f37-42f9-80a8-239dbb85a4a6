import React, { useState, useContext, useEffect, useRef } from "react";
import '../assests/css/filter/contactstable.css';
import PaginationRender from '../pagination/PaginationRender.js';
import DashboardContext from '../common-files/ContextDashboard.js';
import { postWithToken, PostWithTokenNoCache } from '../common-files/ApiCalls.js';
import { ApiName } from '../common-files/ApiNames';
import ContactModal from './ContactModal';
import RS_Animation from '../layouts/RS_Animation.js';
import { getSessionItem } from '../common-files/LocalStorage';
import Search_Us from '../layouts/Search_us';
import UseTabStore from "../common-files/useGlobalState";
import loadingGif from '../assests/waiting.gif';
import { useNavigate } from "react-router-dom";
import Alert from "../common-files/alert.js";
import Loader from "../common-files/Loader.js";
import Analytics from "../../utils/analyticsTracking.js";
const Contactstable = ({ paginationDataCount }) => {
    const keysToCheck = [
        "company_address_country",
        "company_address_state",
        "company_address_city",
        "company_address_zipcode",
        "contact_job_title_1",
        "contact_job_title_level_1",
        "contact_job_dept_name_1",
        "contact_job_function_name_1",
        "company_industries",
        "company_sic_code",
        "company_employee_size",
        "company_annual_revenue_amount",
        "company_tech_keywords_list",
        "company_company_name",
        "company_website",
        "company_type",
        "company_buzzwords_list"
    ];
    const navigate = useNavigate();
    const alertRef = useRef();
    const closeAlertRef = useRef();
    const [loading, setLoading] = useState(true);
    const { dataDC, setDataDC } = useContext(DashboardContext);
    const [data, setData] = useState([]);
    const [showContactModal, setShowContactModal] = useState(true);
    const [isFilterApplied, setIsFilterApplied] = useState(false);
    const {
        setNoOfContact,
        unSelectedRows,
        selectedRows,
        advancedFilter,
        advancedFilterSearchPattern,
        foundCounts,
        currentSelectedPage,
        viewSelected,
        defaultAlert,
        defaultErrorMsg,
        contactModelId,
        selectedContact,
        sortingBy,
        isLowViewCredits,
        viewModal,
        pageSize,
        currentPage,
        selectedTab,
        companyAddressCountry,
        companyAddressState,
        companyAddressCity,
        companyZipCode,
        industryData,
        sicCode,
        companyTechKeyWordsList,
        companyName,
        contactJobTitle1,
        contactJobTitleLevel1,
        resetPage,
        companyURL,
        companyType,
        withDownloaded,
        applyFilters,
        companyKeyword,
        setSelectedTab,
        setSearchPattern,
        setSelectedContactJobDeptName1,
        setSelectedContactJobFunctionName1,
        setIsLowViewCredits,
        setDefaultAlert,
        setDefaultErrorMsg,
        setViewModal,
        setSelectedContact,
        setButtonType,
        setIsFiltered,
        setFoundCounts,
        setSelectedRows,
        setOriginalFoundCounts,
        setEmailRevealedHistory,
        setRedisKey
    } = UseTabStore();

    const localStorageSearchPattern = UseTabStore((state) => state.searchPattern);
    const jobDept = UseTabStore((state) => state.checkedItems);

    let contact_job_title_1 = null;
    let company_employee_size = null;
    let company_annual_revenue_amount = null;
    let dept = [];
    let func = [];
    let count = 1;
    let token = null;
    if ("token" in dataDC) token = dataDC.token;
    let user = getSessionItem("user");
    user = user ? JSON.parse(user) : null;
    if (user && "token" in user) token = user.token;

    let userCredits = getSessionItem("userCredits");
    userCredits = userCredits ? JSON.parse(userCredits) : null;

    const cleanSearchPattern = (searchPattern) => {

        if (!searchPattern) return {};
        const cleanedPattern = {};

        // Iterate through search pattern and only keep non-empty objects
        Object.keys(searchPattern).forEach(key => {
            if (typeof searchPattern[key] === 'object') {
                // If it's an object, only include if it has properties
                if (Object.keys(searchPattern[key]).length > 0) {
                    cleanedPattern[key] = searchPattern[key];
                }
            } else {
                // For non-objects (like strings, numbers, booleans), always include
                // But skip certain control properties
                if (key !== 'searchBy' && key !== 'sortBy' && key !== 'withDownloaded') {
                    cleanedPattern[key] = searchPattern[key];
                }
            }
        });

        return cleanedPattern;
    };
    
    const loadCurrentPageEmailVerifyHistory = async (ids) => {
        try {
            let data_ids = [...ids];
            const payload = data_ids.length > 0 ? { data_ids } : {};
            await PostWithTokenNoCache(ApiName.emailVerifyHistory, payload)
                .then(function (response) {
                    if (response.data.status === 200) {
                        const dataObj = JSON.parse(response.data.data);
                        setEmailRevealedHistory(dataObj);
                    }
                })
                .catch(function (error) {
                });
        } catch (error) {
        }
    };

    useEffect(() => {
        // Add event listeners when the component mounts
        document.addEventListener('mousedown', handleOutsideClick);
        document.addEventListener('keydown', handleEscKeyPress);

        // Remove event listeners when the component unmounts
        return () => {
            document.removeEventListener('mousedown', handleOutsideClick);
            document.removeEventListener('keydown', handleEscKeyPress);
        };
    }, [viewModal, isLowViewCredits]);

    const handleOutsideClick = (e) => {
        // Check if the click is outside the modal
        if (viewModal && isLowViewCredits && !e.target.closest('.modal')) {
            close();
        }
    };

    const handleEscKeyPress = (e) => {
        // Check if the pressed key is ESC
        if (viewModal && isLowViewCredits && e.key === 'Escape') {
            close();
        }
    };
    useEffect(() => {
        setSelectedTab("contact");
        const loaderElement = document.querySelector('#table-loader');
        const loaderContactTable = document.querySelector('#cust-contact-table');

        if (loaderElement) {
            loaderElement.style.display = 'block'; // Set display to 'none' if the element exists in the DOM
        }
        if (loaderContactTable) {
            loaderContactTable.style.display = 'none'; // Set display to 'none' if the element exists in the DOM
        }

        if (contactJobTitle1 && contactJobTitle1.length > 0) {
            contact_job_title_1 = contactJobTitle1.map((v) => v.value);
        } else {
            // Handle the case where jobTitle is not an array, e.g., set contact_job_title_1 to an empty array
            contact_job_title_1 = [];
        }

        if ("DeptFunc" in dataDC && dataDC.DeptFunc !== undefined) {
            let obj = dataDC.DeptFunc;
            for (const key in obj) {
                dept.push(key);
                for (const nestedKey in obj[key]) {
                    func.push(nestedKey);
                }
            }
            setSelectedContactJobDeptName1(dept);
            setSelectedContactJobFunctionName1(func);
        }

        if ("company_employee_size" in dataDC && dataDC.company_employee_size !== undefined) {
            company_employee_size = dataDC.company_employee_size.map((v) => v);
        }
        if ("company_annual_revenue_amount" in dataDC && dataDC.company_annual_revenue_amount !== undefined) {
            company_annual_revenue_amount = dataDC.company_annual_revenue_amount.map((v) => v);
        }

        const searchPatterns = {
            "company_address_country": !resetPage ? { ...companyAddressCountry } : {},
            "company_address_state": !resetPage ? { ...companyAddressState } : {},
            "company_address_city": !resetPage ? { ...companyAddressCity } : {},
            "company_address_zipcode": !resetPage ? { ...companyZipCode } : {},
            "contact_job_title_1": !resetPage ? { ...contact_job_title_1 } : {},
            "contact_job_title_level_1": !resetPage ? { ...contactJobTitleLevel1 } : {},
            "contact_job_dept_name_1": !resetPage ? { ...dept } : {},
            "contact_job_function_name_1": !resetPage ? { ...func } : {},
            "company_industries": !resetPage ? { ...industryData } : {},
            "company_sic_code": !resetPage ? { ...sicCode } : {},
            "company_employee_size": !resetPage ? { ...company_employee_size } : {},
            "company_annual_revenue_amount": !resetPage ? { ...company_annual_revenue_amount } : {},
            "company_tech_keywords_list": !resetPage ? { ...companyTechKeyWordsList } : {},
            "company_company_name": !resetPage ? { ...companyName } : {},
            "company_website": !resetPage ? { ...companyURL } : {},
            "company_type": !resetPage ? { ...companyType } : {},
            "company_buzzwords_list": !resetPage ? { ...companyKeyword } : {},
        };
        // const url = ApiName.searchFilters;
        const url = ApiName.esContactFilter;
        let dataPost = {};
        if (!advancedFilter) {
            setSearchPattern(searchPatterns);
            Object.assign(searchPatterns, {
                searchBy: "contact",
                withDownloaded: withDownloaded,
                sortBy: sortingBy
            });
            dataPost = {
                page: viewSelected ? currentSelectedPage || 1 : currentPage || 1,
                pageSize: dataDC.membership === "trail" ? 10 : 25,
                searchPattern: searchPatterns
            };

            const hasValues = keysToCheck.some(key => searchPatterns[key] && Object.keys(searchPatterns[key]).length > 0);
            console.log("hasValues", hasValues);
            if (!hasValues) {
                const hasValues = keysToCheck.some(key => localStorageSearchPattern[key] && Object.keys(localStorageSearchPattern[key]).length > 0);
                if (localStorageSearchPattern && hasValues) {

                    let empSize = localStorageSearchPattern.company_employee_size;
                    let revenueSize = localStorageSearchPattern.company_annual_revenue_amount;
                    let deptFuncSet = [];

                    empSize = Object.values(empSize);
                    revenueSize = Object.values(revenueSize);

                    for (const key in jobDept) {
                        if (Object.hasOwnProperty.call(jobDept, key)) {
                            const element = jobDept[key];
                            if (element) {
                                deptFuncSet[key] = element;
                            }
                        }
                    }
                    setSearchPattern(localStorageSearchPattern);
                    setDataDC({ ...dataDC, company_employee_size: empSize, company_annual_revenue_amount: revenueSize, DeptFunc: deptFuncSet });

                    dataPost = {
                        page: viewSelected ? currentSelectedPage || 1 : currentPage || 1,
                        pageSize: dataDC.membership === "trail" ? 10 : 25,
                        searchPattern: localStorageSearchPattern
                    };
                }
            } else {
                setSearchPattern(searchPatterns);
                Object.assign(searchPatterns, {
                    searchBy: "contact",
                    withDownloaded: withDownloaded,
                    sortBy: sortingBy
                });
                dataPost = {
                    page: viewSelected ? currentSelectedPage || 1 : currentPage || 1,
                    pageSize: dataDC.membership === "trail" ? 10 : 25,
                    searchPattern: searchPatterns
                };
            }
        } else {
            if (userCredits.user_plan_name === "freemium" && advancedFilterSearchPattern.maxContacts > 2000) {
                setButtonType("warning-error");
                setDefaultAlert(true);
                setDefaultErrorMsg("Upgrade your plan to search over 2000 contacts.");
            } else {
                dataPost = {
                    page: advancedFilterSearchPattern.page,
                    pageSize: advancedFilterSearchPattern.pageSize,
                    searchPattern: advancedFilterSearchPattern.searchPattern,
                    maxContactPerCompany: advancedFilterSearchPattern.maxContactPerCompany,
                    maxContacts: advancedFilterSearchPattern.maxContacts
                };
            }
        }
        const fetchAdvancedFilterCount = async () => {
            try {
                dataPost["page"] = 1;
                const url = ApiName.esAdvancedSearchFilterCount;
                const res = await PostWithTokenNoCache(url, dataPost);
                if (res != null && "status" in res) {
                    if (res.data.status === 200) {
                        const dataObj = JSON.parse(res.data.data);
                        if (dataObj.counts > 0) {
                            // let resData = dataObj.records;
                            setData([]);
                            let dataObj = JSON.parse(res.data.data);
                            let itemsPerPage = dataDC.membership === "trail" ? 10 : 25;
                            let startIndex = (currentPage - 1) * itemsPerPage;
                            let endIndex = startIndex + itemsPerPage;

                            let allIds = dataObj.records.slice().sort((a, b) => a - b);

                            let initialIds = allIds.slice(startIndex, endIndex).sort((a, b) => a - b);
                            let id = initialIds.map(row => row);

                            setSelectedRows(allIds);
                            setFoundCounts(dataObj.counts);
                            fetchAdvancedFilterESData(id);
                        } else {
                            setFoundCounts(0);
                            setIsFilterApplied(false);
                            setLoading(false);
                            setData([]);
                            setSelectedRows([]);
                            if (loaderElement) {
                                loaderElement.style.display = 'none'; // Set display to 'none' if the element exists in the DOM
                            }
                            if (loaderContactTable) {
                                loaderContactTable.style.display = 'block'; // Set display to 'none' if the element exists in the DOM
                            }
                        }
                    }
                }
            } catch (error) {

                fetchAdvancedFilterContacts();
            }
        };

        const fetchAdvancedFilterContacts = async () => {
            try {
                dataPost["page"] = 1;
                const url = ApiName.esAdvancedFilterContacts;

                // Track the "User Initiated Contact Search" event for advanced search
                const user = getSessionItem("user");
                const userData = user ? JSON.parse(user) : null;

                // Clean the search pattern to remove empty objects
                const cleanedSearchPattern = cleanSearchPattern(dataPost.searchPattern);

                // Count the filters in advanced search - only include non-empty objects
                const filterKeys = Object.keys(cleanedSearchPattern).filter(key => {
                    return cleanedSearchPattern[key] &&
                        typeof cleanedSearchPattern[key] === 'object';
                });

                // Count total filters applied
                const filtersCount = filterKeys.length;

                // Extract employee size filter details if present
                const employeeSizeFilter = cleanedSearchPattern.company_employee_size || {};
                const employeeSizeValues = Object.values(employeeSizeFilter);

                // Track the advanced search event with detailed filter information - only send non-empty values
                Analytics.track('User Initiated Contact Search', {
                    search_source: 'contact_tab',
                    search_type: 'advanced',
                    filters_count: filtersCount,
                    filter_types: filterKeys,
                    max_contacts: dataPost.maxContacts || 0,
                    max_contacts_per_company: dataPost.maxContactPerCompany || 0,
                    has_employee_size_filter: Object.keys(employeeSizeFilter).length > 0,
                    employee_size_values: employeeSizeValues.length > 0 ? employeeSizeValues : undefined,
                    applied_filters: cleanedSearchPattern, // Renamed from search_pattern to applied_filters
                    user_email: userData?.email || 'unknown',
                    timestamp: new Date().toISOString()
                });
                const res = await PostWithTokenNoCache(url, dataPost);
                if (res != null && "status" in res) {
                    if (res.data.status === 200) {
                        const dataObj = JSON.parse(res.data.data);
                        if (parseInt(dataObj.counts) < parseInt(advancedFilterSearchPattern.maxContacts)) {
                            setNoOfContact(parseInt(dataObj.counts))
                        }
                        if (dataObj.counts > 0) {
                            // let resData = dataObj.records;
                            setData([]);
                            let dataObj = JSON.parse(res.data.data);
                            setRedisKey(dataObj.key);
                            let uniqueRecords = [...new Set(dataObj.records)];
                            // console.log("total records:",dataObj.records.length);
                            // console.log("unique records:",uniqueRecords.length);
                            let itemsPerPage = dataDC.membership === "trail" ? 10 : 25;
                            let startIndex = (currentPage - 1) * itemsPerPage;
                            let endIndex = startIndex + itemsPerPage;

                            let allIds = uniqueRecords.slice().sort((a, b) => a - b);

                            let initialIds = allIds.slice(startIndex, endIndex).sort((a, b) => a - b);
                            let id = initialIds.map(row => row);

                            // Track successful advanced search with Mixpanel
                            const user = getSessionItem("user");

                            setSelectedRows(allIds);
                            setFoundCounts(dataObj.counts);
                            fetchAdvancedFilterESData(id);
                        } else {
                            // Track zero results advanced search with Mixpanel
                            const user = getSessionItem("user");

                            setFoundCounts(0);
                            setIsFilterApplied(false);
                            setLoading(false);
                            setData([]);
                            setSelectedRows([]);
                            if (loaderElement) {
                                loaderElement.style.display = 'none'; // Set display to 'none' if the element exists in the DOM
                            }
                            if (loaderContactTable) {
                                loaderContactTable.style.display = 'block'; // Set display to 'none' if the element exists in the DOM
                            }
                        }
                    }
                }
            } catch (error) {
                // Track advanced search error with Mixpanel
                const user = getSessionItem("user");
                const userData = user ? JSON.parse(user) : null;

                if (count < 4) {
                    const loaderElement = document.querySelector('#total_contacts');

                    if (loaderElement) {
                        loaderElement.textContent = 'Retrying...'; // Use textContent to set the text
                    }
                    fetchAdvancedFilterCount();
                    count++;

                    // Track retry attempt
                    Analytics.trackFeatureUsage('Count Fetch Retry', {
                        user_email: userData?.email || 'unknown',
                        retry_count: count,
                        search_pattern: JSON.stringify(dataPost.searchPattern)
                    });
                } else {

                    const loaderElement = document.querySelector('#table-loader');
                    const loaderContactTable = document.querySelector('#cust-contact-table');

                    if (loaderElement) {
                        loaderElement.style.display = 'none'; // Set display to 'none' if the element exists in the DOM
                    }
                    if (loaderContactTable) {
                        loaderContactTable.style.display = 'block'; // Set display to 'none' if the element exists in the DOM
                    }
                    setButtonType("error");
                    setDefaultErrorMsg("Sorry.! Please try again");
                    setDefaultAlert(true);
                    setFoundCounts(-1);
                    setLoading(false);
                }
            }
        };

        const fetchAdvancedFilterESData = async (id) => {
            try {
                let url = ApiName.esAdvancedSearchFilter;
                setLoading(true);
                dataPost["includedIds"] = id;
                dataPost["maxContacts"] = dataDC.membership === "trail" ? 10 : 25;
                // Do not change to postWithToken, selected data checkboxs logic get's wrong
                const res = await PostWithTokenNoCache(url, dataPost);
                if (res != null && "status" in res) {
                    if (res.data.status === 200) {

                        let dataObj = JSON.parse(res.data.data);

                        if (dataObj.records.length < 1) {
                            setFoundCounts(0);
                            setIsFilterApplied(false);
                            setLoading(false);
                            setData([]);
                            setSelectedRows([]);
                        } else {
                            setIsFilterApplied(true);
                            setData(dataObj.records);
                            const ids = dataObj.records.map(item => item.id);
                            loadCurrentPageEmailVerifyHistory(ids);
                            localStorage.setItem('loadedIds', JSON.stringify(ids));
                            setLoading(false);
                        }
                        if (loaderElement) {
                            loaderElement.style.display = 'none'; // Set display to 'none' if the element exists in the DOM
                        }
                        if (loaderContactTable) {
                            loaderContactTable.style.display = 'block'; // Set display to 'none' if the element exists in the DOM
                        }
                    }
                }
            } catch (error) {
                setLoading(false);
                setData([]);

                const loaderElement = document.querySelector('#table-loader');
                const loaderContactTable = document.querySelector('#cust-contact-table');

                if (loaderElement) {
                    loaderElement.style.display = 'none'; // Set display to 'none' if the element exists in the DOM
                }
                if (loaderContactTable) {
                    loaderContactTable.style.display = 'block'; // Set display to 'none' if the element exists in the DOM
                }
                setButtonType("error");
                setDefaultErrorMsg("Sorry.! Please try again");
                setDefaultAlert(true);
                setFoundCounts(-1);
                setLoading(false);
            }
        };

        const fetchData = async () => {
            try {
                setLoading(true);
                // Do not change to postWithToken, selected data checkboxs logic get's wrong
                const res = await PostWithTokenNoCache(url, dataPost);
                if (res != null && "status" in res) {
                    if (res.data.status === 200) {
                        const dataObj = JSON.parse(res.data.data);
                        if (dataObj.records.length < 1) {
                            setFoundCounts(-1);
                            setIsFilterApplied(false);
                        } else {
                            setIsFilterApplied(true);
                            setData(dataObj.records);
                            const ids = dataObj.records.map(item => item.id);
                            loadCurrentPageEmailVerifyHistory(ids);
                            setLoading(false);
                        }
                        if (loaderElement) {
                            loaderElement.style.display = 'none'; // Set display to 'none' if the element exists in the DOM
                        }
                        if (loaderContactTable) {
                            loaderContactTable.style.display = 'block'; // Set display to 'none' if the element exists in the DOM
                        }
                    }
                }
            } catch (error) {
                setLoading(false);
                setData([]);

                const loaderElement = document.querySelector('#table-loader');
                const loaderContactTable = document.querySelector('#cust-contact-table');

                if (loaderElement) {
                    loaderElement.style.display = 'none'; // Set display to 'none' if the element exists in the DOM
                }
                if (loaderContactTable) {
                    loaderContactTable.style.display = 'block'; // Set display to 'none' if the element exists in the DOM
                }
                setButtonType("error");
                setDefaultErrorMsg("Sorry.! Please try again");
                setDefaultAlert(true);
                setFoundCounts(-1);
                setLoading(false);
            }
        };

        const fetchESData = async () => {
            try {
                setLoading(true);
                cahcedContactData(dataPost);
                // Do not change to postWithToken, selected data checkboxs logic get's wrong
                const res = await PostWithTokenNoCache(url, dataPost);
                if (res != null && "status" in res) {
                    if (res.data.status === 200) {
                        const dataObj = JSON.parse(res.data.data);
                        if (dataObj.totalCount > 0) {
                            setFoundCounts(dataObj.totalCount);
                            setOriginalFoundCounts(dataObj.totalCount);
                            setIsFilterApplied(true);
                            setData(dataObj.records);
                            const ids = dataObj.records.map(item => item.id);
                            localStorage.setItem('loadedIds', JSON.stringify(ids));
                            loadCurrentPageEmailVerifyHistory(ids);

                            setLoading(false);

                            if (loaderElement) {
                                loaderElement.style.display = 'none'; // Set display to 'none' if the element exists in the DOM
                            }
                            if (loaderContactTable) {
                                loaderContactTable.style.display = 'block'; // Set display to 'none' if the element exists in the DOM
                            }

                        } else {
                            setFoundCounts(-1);
                            setIsFilterApplied(true);
                            setLoading(false);
                            setData([]);
                            if (loaderElement) {
                                loaderElement.style.display = 'none'; // Set display to 'none' if the element exists in the DOM
                            }
                            if (loaderContactTable) {
                                loaderContactTable.style.display = 'block'; // Set display to 'none' if the element exists in the DOM
                            }
                        }
                    }
                }
            } catch (error) {
                if (error.response.status == 403) {
                    setButtonType("error");
                    setDefaultErrorMsg("Sorry.! Please contact admin");
                } else {
                    setButtonType("error");
                    setDefaultErrorMsg("Sorry.! Please try again");
                }
                setFoundCounts(-1);
                setIsFilterApplied(true);
                setLoading(false);
                setData([]);

                const loaderElement = document.querySelector('#table-loader');
                const loaderContactTable = document.querySelector('#cust-contact-table');

                if (loaderElement) {
                    loaderElement.style.display = 'none'; // Set display to 'none' if the element exists in the DOM
                }
                if (loaderContactTable) {
                    loaderContactTable.style.display = 'block'; // Set display to 'none' if the element exists in the DOM
                }
                setDefaultAlert(true);
                setFoundCounts(-1);
                setLoading(false);
            }
        };

        const fetchTotalCount = async () => {
            let totalCount = {
                "searchPattern": dataPost.searchPattern
            };

            const updatedCompanyTechKeyWordsList = Object.keys(companyTechKeyWordsList).reduce((acc, key) => {
                acc[key] = companyTechKeyWordsList[key].toLowerCase();
                return acc;
            }, {})
            dataPost.searchPattern["company_tech_keywords_list"] = updatedCompanyTechKeyWordsList;

            await PostWithTokenNoCache(ApiName.totalContactCounts, totalCount)
                .then(function (response) {
                    if (response.data.status === 200) {
                        const dataObj = JSON.parse(response.data.data);
                        if (dataObj.counts > 0) {
                            setFoundCounts(dataObj.counts);
                            setOriginalFoundCounts(dataObj.counts);
                            fetchData();
                        } else {
                            setFoundCounts(0);
                            setIsFilterApplied(false);
                            setLoading(false);
                            setData([]);
                            if (loaderElement) {
                                loaderElement.style.display = 'none'; // Set display to 'none' if the element exists in the DOM
                            }
                            if (loaderContactTable) {
                                loaderContactTable.style.display = 'block'; // Set display to 'none' if the element exists in the DOM
                            }
                        }
                    }
                }).catch(function (error) {
                    if (count == 1) {
                        const loaderElement = document.querySelector('#total_contacts');

                        if (loaderElement) {
                            loaderElement.textContent = 'Retrying...'; // Use textContent to set the text
                        }
                        fetchTotalCount();
                        count++;
                    } else {

                        const loaderElement = document.querySelector('#table-loader');
                        const loaderContactTable = document.querySelector('#cust-contact-table');

                        if (loaderElement) {
                            loaderElement.style.display = 'none'; // Set display to 'none' if the element exists in the DOM
                        }
                        if (loaderContactTable) {
                            loaderContactTable.style.display = 'block'; // Set display to 'none' if the element exists in the DOM
                        }
                        setButtonType("error");
                        setDefaultErrorMsg("Sorry.! Please try again");
                        setDefaultAlert(true);
                        setFoundCounts(-1);
                        setLoading(false);
                    }
                });
        }

        const cahcedContactData = async (params) => {
            try {
                await PostWithTokenNoCache(ApiName.cachedContactData, params);
            } catch (error) {
                if (error) {
                    return;
                }
            }
        };

        const hasValues = keysToCheck.some(key => {
            const searchPatternValue = dataPost.searchPattern[key];
            return searchPatternValue && Object.keys(searchPatternValue).length > 0;
        });

        if (hasValues && !advancedFilter) {
            setIsFiltered(true);
            //fetchTotalCount();
            fetchESData();
        } else if (hasValues && advancedFilter) {
            if (selectedRows.length <= 0) {
                fetchAdvancedFilterContacts();
                // fetchAdvancedFilterCount();
                // fetchAdvancedFilterESData();
            } else {
                setIsFiltered(true);
                setData([]);
                let newSelectedRows = [...selectedRows, ...unSelectedRows].sort((a, b) => a - b);
                let itemsPerPage = dataDC.membership === "trail" ? 10 : 25;
                let startIndex = (currentPage - 1) * itemsPerPage;
                let endIndex = startIndex + itemsPerPage;

                let initialIds = newSelectedRows.slice(startIndex, endIndex);
                let id = initialIds.map(row => row);
                fetchAdvancedFilterESData(id);
            }
        } else {
            setIsFiltered(false);
            setFoundCounts(-1);
            setIsFilterApplied(false);
            setLoading(false);
            setData([]);
            if (loaderElement) {
                loaderElement.style.display = 'none'; // Set display to 'none' if the element exists in the DOM
            }
            if (loaderContactTable) {
                loaderContactTable.style.display = 'block'; // Set display to 'none' if the element exists in the DOM
            }
        }
    }, [
        resetPage,
        applyFilters,
        currentPage,
        currentSelectedPage,
        advancedFilter
    ]);

    useEffect(() => {
        fetchSingleContact(contactModelId)
    }, [contactModelId]);

    const urlSingleFetch = ApiName.fetchSingleRecord;
    async function fetchSingleContactRecord(data, total_balance_contact_view, total_balance_credit) {
        const addActionKey = {
            ...data,
            action: "view",
            searchBy: selectedTab,

        }
        const res = await postWithToken(urlSingleFetch, addActionKey);
        if (res && "status" in res) {
            if (res.data.status === 200) {
                let dataObj = JSON.parse(res.data.data);
                if (dataObj[0] === undefined || dataObj[0] === null) return;
                if (showContactModal) {
                    setIsLowViewCredits(false);
                    setSelectedContact(dataObj[0]);
                    if (total_balance_contact_view != null) {
                        let totalBalanceContactView = total_balance_contact_view;
                        if ("membership" in dataDC && dataDC.membership === "prime") {
                            totalBalanceContactView += 1;
                        } else {
                            totalBalanceContactView -= 1;
                        }
                        const requestBody = {
                            total_balance_contact_view: totalBalanceContactView,
                            total_balance_credit: total_balance_credit
                        };
                    }
                }
            }
        }
    }

    const fetchSingleContact = async (dataId) => {
        if (dataId) {
            const data = { "id": dataId };
            if ("membership" in dataDC && dataDC.membership === "prime") {
                // fetchSingleContactRecord(data,null,null);
                try {
                    const activeCreaditsFetchRes = await PostWithTokenNoCache(ApiName.activeCredit, {});
                    if (activeCreaditsFetchRes && "status" in activeCreaditsFetchRes) {
                        if (activeCreaditsFetchRes.data.status === 200) {
                            let dataObj = JSON.parse(activeCreaditsFetchRes.data.data);
                            let total_balance_contact_view = "total_balance_contact_view" in dataObj ? dataObj.total_balance_contact_view : null;
                            if (total_balance_contact_view == "Unlimited") total_balance_contact_view = 0;
                            let total_balance_credit = "total_balance_credit" in dataObj ? dataObj.total_balance_credit : null;
                            if (total_balance_contact_view !== undefined && total_balance_contact_view !== null && Number(total_balance_contact_view) != NaN) {
                                fetchSingleContactRecord(data, Number(total_balance_contact_view), Number(total_balance_credit));
                            } else {
                                setSelectedContact(null);
                                setIsLowViewCredits(true);
                            }
                        }
                    } else {
                        setSelectedContact(null);
                        setIsLowViewCredits(true);
                    }
                } catch (error) {
                    setButtonType("error");
                    setDefaultErrorMsg(error.response.data.message);
                    setDefaultAlert(true);
                }
            } else {
                try {
                    const activeCreaditsFetchRes = await PostWithTokenNoCache(ApiName.activeCredit, {});
                    if (activeCreaditsFetchRes && "status" in activeCreaditsFetchRes) {
                        if (activeCreaditsFetchRes.data.status === 200) {
                            let dataObj = JSON.parse(activeCreaditsFetchRes.data.data);
                            let total_balance_contact_view = "total_balance_contact_view" in dataObj ? dataObj.total_balance_contact_view : null;
                            let total_balance_credit = "total_balance_credit" in dataObj ? dataObj.total_balance_credit : null;
                            if (total_balance_contact_view && Number(total_balance_contact_view) != NaN && Number(total_balance_contact_view) > 0 || total_balance_contact_view == "Unlimited") {
                                fetchSingleContactRecord(data, Number(total_balance_contact_view), Number(total_balance_credit));
                            } else {
                                setSelectedContact(null);
                                setIsLowViewCredits(true);
                            }
                        }
                    } else {
                        setSelectedContact(null);
                        setIsLowViewCredits(true);
                    }
                } catch (error) {
                    setButtonType("error");
                    setDefaultErrorMsg(error.response.data.message);
                    setDefaultAlert(true);
                }
            }
        }
    };

    useEffect(() => {
        if (viewModal && isLowViewCredits) {
            setDefaultAlert("error");
            setDefaultErrorMsg("You have exceeded your view credits");
            alertRef.current.click();
        }
    }, [viewModal, isLowViewCredits])

    const close = () => {
        setButtonType("");
        setDefaultAlert(false);
        setDefaultErrorMsg("");
        setViewModal(false);
        closeAlertRef.current.click();
    }

    const upgradeNow = () => {
        closeAlertRef.current.click();
        navigate("/payment-details");
    }
    if (loading) {
        return <Loader />;
    }
    return (
        <div id="cust-contact-table" style={{ display: 'block' }}>
            {/* {console.log("foundCounts", data)} */}
            {/* {console.log("loading", loading ? "True" : "False")}
            {console.log("isFilterApplied", isFilterApplied ? "True" : "False")} */}
            {
                foundCounts == -1 && !loading && !isFilterApplied ? (
                    <RS_Animation />
                ) : !foundCounts && foundCounts !== -1 && isFilterApplied && !loading ? (
                    <RS_Animation />
                ) : !foundCounts && !isFilterApplied && !loading ? (
                    <RS_Animation />
                ) : foundCounts == -1 && !loading && isFilterApplied ? (
                    <Search_Us />
                ) : foundCounts == 0 && !isFilterApplied && !loading ? (
                    <Search_Us />
                ) : foundCounts && !isFilterApplied && !loading ? (
                    <Search_Us />
                ) : !loading && foundCounts >= 0 && foundCounts != -1 && isFilterApplied ? (
                    <PaginationRender
                        key={foundCounts}
                        foundCounts={foundCounts}
                        pageNumber={currentPage}
                        pageCount={pageSize}
                        data={data}
                        loadingCount={loading}
                        paginationDataCount={paginationDataCount}
                    />
                ) : (
                    <div className="loader-container" style={{ display: 'none', margin: "9rem 0 0 0" }}>
                        <img src={loadingGif} width="300" alt="Loading" className="m-auto d-block" />
                    </div>
                )}
            {viewModal && isLowViewCredits ? (
                <>
                    <button
                        type="button"
                        className="btn btn-info btn-md"
                        data-toggle="modal"
                        data-target="#alertModal1"
                        ref={alertRef}
                        style={{ display: 'none' }} // Hide the button, it's just for triggering click programmatically
                        data-backdrop="true"
                    ></button>
                    <div className="modal fade bd-example-modal-sm show" tabIndex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true" id="alertModal1">
                        <div className="modal-dialog modal-sm" >
                            <div className="modal-content" style={{ width: "376px", borderRadius: "16px", position: "absolute", top: "14rem" }} >
                                <div className="d-flex flex-row justify-content-end pt-2 pr-3 pb-2">
                                    <button
                                        ref={closeAlertRef}
                                        type="button"
                                        className="close"
                                        data-dismiss="modal"
                                        aria-label="Close"
                                        onClick={close}
                                        style={{ display: 'none' }} // Hide the button, it's just for triggering click programmatically
                                    />
                                    <div onClick={close}>
                                        <img src="../images/cross.png" width="20" style={{ cursor: "pointer" }} />
                                    </div>
                                </div>
                                <div className="d-flex flex-row justify-content-center">
                                    <div>
                                        <img src="../images/grey-avetar.png" width="50" />
                                    </div>
                                    <div>
                                        <p className="grey-avetar">!</p>
                                    </div>
                                    <div>
                                        <p className="anerror">An Error Occurred</p>
                                    </div>
                                </div>

                                <div className="lorem">
                                    <p>You have exceeded your view credits</p>
                                </div>

                                <div className="upgrdbutton">
                                    <button type="button" onClick={upgradeNow}>Upgrade</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </>
            ) : selectedContact && viewModal ? (
                <ContactModal isLowViewCredits={isLowViewCredits} />
            ) : !loading && defaultAlert ? (
                <Alert data={defaultErrorMsg} />
            ) : (<></>)}
        </div >
    )
}

export default Contactstable;