@import url('https://fonts.googleapis.com/css2?family=Kanit:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Lato:ital,wght@0,100;0,300;0,400;0,700;0,900;1,100;1,300;1,400;1,700;1,900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

body{
  font-family: 'Lato', 'medium';
}

.secondary-square {
  background-color: #ebf6ff;
  /* margin-top: 23px; */
  position: relative;
  /* height: 40vh; */

}

.primary-square {
  box-shadow: rgb(50 50 93 / 25%) 0px 2px 5px -1px,
    rgb(0 0 0 / 30%) 0px 1px 3px -1px;
  /* height: 27vh; */
}

.arrow img {
  padding: 14px 6px 0 0px;
}

.Ken-Hogan h3 {
  font-size: 16px;
  margin-left: 11px;
  font-family: "Lato", Regular;
  font-weight: 600;
  position: relative;
  top: 53px;
}

.Ken-Hogan p {
  font-size: 12px;
  margin-left: 12px;
  font-family: "Lato", Regular;
  position: relative;
  top: 54px;
}



.availables h3 {
  text-align: center;
  font-size: 20px;
  margin: 0;
  padding: 10px 0 6px 0;
  font-family: "Lato", Regular;
  -webkit-text-stroke-width: thin;
}

.availables p {
  text-align: center;
  font-size: 14px;
  font-weight: bold;
}



.upgrade button {
  color: #fff;
  text-decoration: none;
  font-family: "Lato", Regular;
}

hr.horizontal2 {
  border-bottom: 2px solid #093d54;
  width: 64px;
  margin: auto;
  display: inline-table;
  margin-bottom: 2px;
  color: #093d54;
}

.sticky-container {
  position: relative;
  height: 300px;
  /* Set a fixed height for the container */
  /* Other styles for the container */
}

.sticky-box {
  position: sticky;
  top: 0;
  z-index: 1;
  background-color: white;
  /* Set the background color of the box */
  /* Other styles for the box */
}

.scrollable-content {
  overflow-y: auto;
  height: 135%;
  box-shadow: rgb(99 99 99 / 20%) 0px 2px 8px 0px;
}

.personal-information {
  margin-left: 0%;
  margin-top: 18px;
  color: #093d54;
}

.personal-information h4 {
  margin: 0;
  font-family: "Lato", Regular;
  font-size: 17px;
  font-weight: 600;
}

.horizontal3 {
  border-bottom: 2px solid #093d54;
  width: 64px;
  margin: auto;
  display: inline-table;
  margin-bottom: 4px;
  color: #093d54;
}

.content-aria {
  padding: 0 10rem 2rem 10rem;
  background-color: #fff;
}

label.label {
  color: #000;
  font-family: Lato, Regular;
  font-size: 14px;
  font-weight: bolder;
}

.form-group {
  margin-bottom: 12px;
}

i.fas.fa-arrow-left {
  font-size: 17px;
  margin-top: 12px;
  padding: 0 10px 0 0px;
  color: #55c2c3;
  cursor: pointer;
}

.arrow h3 {
  font-size: 20px;
  padding: 6px 0 0 4px;
  color: #093d54;
  font-family: "Lato", Regular;
  -webkit-text-stroke-width: thin;
}

label.form-check-label {
  font-size: 14px;
  color: #000000;
  font-family: "Lato", Regular;
}

.update-\&-save {
  border: 0;
  outline: none;
  margin-top: 22px;
  margin-bottom: 8px;
}

.update-\&-save button {
  border: 0;
  outline: none;
  background-color: #093d54;
  color: #fff;
  padding: 7px 15px 7px 15px;
  border-radius: 12px;
  cursor: pointer;
  font-family: "Lato", Regular;
}

.upload-profile {
  /* cursor: pointer; */
  margin-top: 24px;
  margin-bottom: 23px;
}


input#firstName {
  /* width: 260px; */
  padding: 6px 0 6px 8px;
}

input#lastname {
  /* width: 260px; */
  padding: 6px 0 6px 8px;
}

input#exampleInputEmail21 {
  /* width: 260px; */
  padding: 6px 0 6px 8px;
}

input#phone {
  /* width: 260px; */
  /* padding: 6px 0 6px 8px; */
}

input#Password {
  /* width: 260px; */
  padding: 6px 0 6px 8px;
}

input#Jobtitle {
  /* width: 260px; */
  padding: 6px 0 6px 8px;
}

input#linkedin {
  /* width: 260px; */
  /* padding: 6px 0 6px 8px; */
}

input#facebook {
  /* width: 260px; */
  /* padding: 6px 0 6px 8px; */
}

input#twitter {
  /* width: 260px; */
  /* padding: 6px 0 6px 8px; */
}

input#instagram {
  /* width: 260px; */
  /* padding: 6px 0 6px 8px; */
}

input#company_Name {
  /* width: 260px; */
  /* padding: 6px 0 6px 8px; */
}

input#website_address {
  /* width: 260px; */
  /* padding: 6px 0 6px 8px; */
}

input#Industry {
  /* width: 260px; */
  /* padding: 6px 0 6px 8px; */
}

input#employee-size {
  /* width: 260px; */
  /* padding: 6px 0 6px 8px; */
}

input#revenue {
  /* width: 260px; */
  /* padding: 6px 0 6px 8px; */
}

input#company_address {
  /* width: 260px; */
  padding: 6px 0 6px 8px;
}

input#state {
  /* width: 260px; */
  /* padding: 6px 0 6px 8px; */
}

input#city {
  /* width: 260px; */
  /* padding: 6px 0 6px 8px; */
}

input#zip_code {
  /* width: 260px; */
  padding: 6px 0 6px 8px;
}

.input-container {
  position: relative;
}

.input-container img {
  position: absolute;
  top: 0;
  left: 86%;
  height: 100%;
  padding: 7px;
  box-sizing: border-box;
}

.input-container input {
  padding: 10px;
  padding-left: 50px;
  /* Leave space for the image */
  box-sizing: border-box;
}

.modal-dialog-true.modal-sm {
  margin: auto;
  margin-top: 15%;
}

.header-modal p {
  text-align: center;
  padding: 20px 0px 0px 0px;
  color: #093d54;
  font-size: 20px;
  font-weight: 500;
}

.upgrade button {
  background-color: #093d54;
  border: 0;
  cursor: pointer;
  font-size: 12px;
  margin-top: 0;
  outline: none;
  padding: 3px;
}

.small-up {
  margin-top: 15%;
  margin-left: 5%;
}

.-my-modal-dialog {
  width: 330px;
  margin: auto;
  margin-top: 18%;
  /* height: 402px; */
}

.my-modal-header {
  text-align: center;
}

h5#exampleModalLongTitle {
  font-size: 20px;
  padding: 22px 0 0 20px;
  color: #093d54;
  -webkit-text-stroke: thin;
}

.save {
  text-align: center;
  margin-bottom: 10px;
}

/* .save button {
  padding: 0 20px 2px 20px;
  outline: none;
  border: 0;
  font-size: 14px;
  background-color: #093d54;
  color: #fff;
  border-radius: 6px;
} */

.cursor {
  padding: 0 20px 15px 0px;
}

span.upld-img {
  padding: 0 8px 0px 0px;
}

p.upload-text {
  font-size: 14px;
  color: #000;
  padding: 0px 0 10px 0;
  cursor: pointer;
  -webkit-text-stroke-width: thin;
}

.rounded-image {
  border-radius: 50%;
}

.form-check {
  position: relative;
  display: block;
  padding: 0 0 0 3px;
}

.cursor img {
  width: 128px;
  box-shadow: rgb(100 100 111 / 20%) 0px 7px 29px 0px;
  border-radius: 84px;
  margin-top: 29px;
}

button.close {
  color: #55c2c3 !important;
  padding: -4px 12px 4px 0px;
  font-weight: 500;
  font-size: 30px;
  outline: none;
}

.highlight-red {
  text-align: center;
  top: 433px;
  left: 579px;
  width: 295px;
  height: 32px;
  text-align: center;
  font: normal normal normal 12px/17px Lato;
  letter-spacing: 0px;
  color: #6DE1A4;
  opacity: 1;
  margin-bottom: 14px;
}

.highlight-blue-gray-shade {
  text-align: center;
  top: 433px;
  left: 579px;
  width: 295px;
  height: 32px;
  text-align: center;
  font: normal normal normal 12px/17px Lato;
  letter-spacing: 0px;
  color: #55c2c3;
  opacity: 1;
  margin-bottom: 14px;
}

.d-flex.flex-column label {
  padding: 0px 0 0 13px;
  font-size: 14px;
  /* -webkit-text-stroke-width: thin; */
  font-weight: 500;
  /* width: 160px; */
  color: #000;
}

.disabled-paragraph {
  /* Styles for the enabled paragraph */
  font-size: 14px;
  color: #000;
  padding: 0px 0 10px 0;
  cursor: pointer;
}

.disabled-paragraph.disabled {
  color: #7e8c9c;
  cursor: not-allowed;
  font-size: 14px;
  padding: 0px 0 10px 0;
  opacity: 1;
  -webkit-text-stroke-width: thin;
}

img.dim-image {
  cursor: not-allowed;
  opacity: 1;
  filter: opacity(0.5);
}

.disabledBtn {
  padding: 0 20px 2px 20px;
  outline: none;
  border: 0;
  font-size: 14px;
  background-color: #c2c8ce;
  color: #fff;
  border-radius: 6px;
  opacity: 1;
  width: 81px;
  height: 25px;
}

.enabledBtn {
  padding: 0 20px 2px 20px;
  outline: none;
  border: 0;
  font-size: 14px;
  background-color: #093d54;
  color: #fff;
  border-radius: 6px;
  width: 81px;
  height: 25px;
  cursor: pointer;
}

img.pro {
  position: relative;
  top: 0;
  left: 0;
}

img.profiles {
  position: absolute;
  top: 110px;
  left: 110px;
  cursor: pointer;
}

.modal-content-upload {
  background-color: #fff;
  margin: auto;
  border-radius: 5px;
}

.form-control:disabled,
.form-control[readonly] {
  background-color: #ebf6ff;
  opacity: 1;
}

span.signup-errors-b p {
  color: #55c2c3;
  font-size: 12px;
  margin: 0;
  padding: 2px 0 0;
}

.account-details-page {
  width: 100%;
  padding: 0 4rem 0 4rem;
}

.total-downloads p {
  text-align: center;
  font-size: 11px;
  font-family: "Lato", Regular;
  /* font-weight: bold; */
  margin-top: 4px;
}


.total-downloads h3 {
  text-align: center;
  font-size: 20px;
  margin: 0;
  padding: 10px 0 6px 0;
  font-family: "Lato", Regular;
  font-weight: 600;
  color: #093D54;
}

.Totals {
  /* border: 1px solid #b8c9e4; */
  background-color: #ffffff;
  /* width: 170px; */
  height: 53px;
  margin-top: 0px;
  /* margin-left: 3rem; */
  border-radius: 10px;
}


.inappp {
  margin-top: 17px;
  border-radius: 10px;
}


.inappp h2 {
  font-size: 20px;
  margin: 0;
  padding: 0;
  color: #093D54;
  font-weight: 600;
}

/* .api {
  position: absolute;
  left: 33rem;
  top: 17px;
} */



.api h2 {
  font-size: 20px;
  margin: 16px 0 7px 0;
  padding: 0;
  color: #093D54;
  font-weight: 600;
}

.Totals-1 {
  background-color: #ffffff;
  margin-top: 7px;
  border-radius: 10px;
  padding: 6px 0px 11px 0px;
  margin-bottom: 20px;
}

button.generate-api-key {
  padding: 4px 2rem 5px 2rem;
  margin: 10px 10px 0 10px;
  border: 0;
  background-color: #093D54;
  color: #fff;
  border-radius: 10px;
  font-size: 12px;
  cursor: pointer;
  width: -webkit-fill-available;
  outline: none;
}


.Totals-2 {
  height: 127px;
  margin-top: 16px;
  width: 160px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: rgb(50 50 93 / 11%) 0px 60px 84px -30px, rgb(10 33 66 / 36%) 0px 1px 5px 0px inset;
}


p.thousand {
  text-align: center;
  font-size: 24px;
  font-weight: bolder;
  color: #6DE1A4;
  margin: 5px 0 0 0;
  padding: 8px 0 0 0px;
}

p.remaining-creadits {
  text-align: center;
  font-size: 14px;
  color: #000;
  font-weight: 600;
}



.upgrade {
  background-color: #093d54;
  border-radius: 8px;
  margin-top: 0px;
  padding: 4px 0px 4px 0px;
  text-align: center;
  margin: 0px 8px 0 8px;
}



span.api-name {
  color: #000000;
  font-size: 12px;
  padding: 0 0 0 6px;
  font-weight: 600;
}


.copied-api {
  background-color: #EEEEEE;
  padding: 6px 20px 5px 20px;
  font-size: 12px;
  margin-left: 1rem;
  border-radius: 13px;
  cursor: copy;
  color: #093D54;
}

.generated-color {
  background-color: #F5F5F5;
  border-radius: 11px;
  padding: 0 0 1px 0px;
  margin: 2px 0 0 0px;
  /* width: -webkit-fill-available; */
}

p.generated-key {
  /* background-color: #F5F5F5; */
  padding: 5px 0px 3px 15px;
  font-size: 12px;
  border-radius: 11px;
  margin: 0 0 0 5px;
  color: #55C2C3;
}

.copy-api {
  background-color: #fff;
  padding: 7px 0 7px 2px;
  border-radius: 7px;
  margin: 55px 8px 7px 2px;
}

.total-icon {
  font-size: 12px;
  padding: 25px 0 0 0;
  position: absolute;
  top: 41px;
  font-weight: 600;
}


h4.total-number {
  color: #093D54;
  font-size: 27px;
  padding: 3px 5px 0px 5px;
  font-weight: 600;
}

h1.user-count {
  color: #093D54;
  font-size: 27px;
  padding: 9px 12px 0px 5px;
  font-weight: 600;
  margin: 0px 0 0 0;
  /* padding: 0; */
}

p.Reach-us {
  text-align: center;
  font-size: 16px;
  color: #000;
  font-weight: 500;
  margin: 15px 0 10px 0px;
}


form.contact-form {
  padding: 12px;
}

.spacer {
  padding: 4px 6px 0 0px;
}

.submitclass {
  border: none;
  padding: 6px 15px 6px 15px;
  border-radius: 7px;
  background-color: #093D54;
  color: #fff;
  cursor: pointer;
}


p.email-founder a {
  color: #002130;
  text-decoration: none;
}





span.creits-used {
  font-size: 12px;
  padding: 0 5rem 0 0px;
  color: #000;
  font-weight: 600;
}

span.creits-sell {
  font-size: 26px;
  font-weight: 500;
  color: #093D54;
  cursor: pointer;
}

span.user-count {
  /* margin: 10px 0 0 0px; */
  /* padding: 74px 0 0 0; */
  margin-top: 21pz;
  position: absolute;
  top: 57px;
  left: 86%;
  font-weight: 600;
}

.contact-popup {
  border: none;
  background-color: #093d54;
  color: #fff;
  padding: 5px 18px 5px 18px;
  border-radius: 7px;
  cursor: pointer;
  outline: none;
}

p.connect-us a {
  text-decoration: none;
  color: #093d54;
  font-weight: 500;
  padding: 0 0 0 8px;
}

.contactus {
  color: #093D54;
  font-weight: 600;
  font-size: 26px;
  margin: 20px 0 0 14px;
}

.reach-usat {
  background-color: #F8F8F8;
}

/*
a.unlock {
  margin: 18px 0px 0 0;
  font-size: 18px;
  text-decoration: none;
  color: #093d54;
  font-weight: 600;
} */

button.apply-butto {
  border: 0;
  outline: none;
  background-color: #093d54;
  color: #fff;
  padding: 3px 14px 3px 14px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
}

.Danger {
  margin: 0;
  padding: 2px 7px 2px 7px;
  font-size: 14px;
}

.success {
  margin: 0;
  padding: 2px 7px 2px 7px;
  font-size: 14px;
}


button.Regenerate {
  border: 0;
  border-radius: 14px;
  font-size: 12px;
  background-color: #093D54;
  color: #F6F6F6;
  padding: 5px 20px 5px 20px;
  cursor: pointer;
  outline: none;
  margin: 1px 0 0 7px;
}


.godown img {
  position: absolute;
  top: 22rem;
  /* z-index: 1; */
  background-color: white;
  margin: auto;
}


p.successmessage3 {
  border: 1px solid #6DE1A4;
  font-size: 10px;
  padding: 0px 18px 0px 3px;
  border-radius: 8px;
  margin: 0px 0 0 0;
  display: block;
  position: absolute;
  background-color: #EEFFF6;
  /* width: 161px; */
  height: 36px;
}


p.successmessage4 {
  border: 1px solid #6DE1A4;
  font-size: 12px;
  padding: 0px 18px 0px 0px;
  border-radius: 8px;
  margin: 3px 0 0 3px;
  display: block;
  position: absolute;
  background-color: #EEFFF6;
  height: 36px;
  top: 44%;
  right: 25%;
}

p.successmessage5 {
  border: 1px solid #6DE1A4;
  font-size: 12px;
  padding: 0px 13px 0px 0px;
  border-radius: 8px;
  margin: 0px 0 0 0;
  display: block;
  position: absolute;
  background-color: #EEFFF6;
  /* width: 170px; */
  height: 36px;
  top: 88%;
  right: 26%;
}

p.successmessage4 img {
  padding: 0px 0 0 0;
  width: 40px;
}



p.successmessage3 img {
  padding: 2px 0 0 0;
  width: 35px;
}

button.deactivate {
  background-color: #f3ebe9;
  border: 0;
  outline: none;
  font-size: 14px;
  font-weight: 600;
  color: #DE350B;
  padding: 8px 20px 8px 20px;
  border-radius: 5px;
  margin: 22px 0 0 0;
  cursor: pointer;
}

p.areyousure {
  text-align: center;
  font-size: 16px;
  font-weight: 600;
  color: #093D54;
}

button.yesno {
  background-color: #093D54;
  color: #fff;
  padding: 4px 23px 4px 23px;
  margin: 0 0 30px 0;
  border: 0;
  cursor: pointer;
  outline: none;
  border-radius: 10px;
}

button.no {
  background-color: #FFF;
  color: #093D54;
  padding: 4px 23px 4px 23px;
  margin: 0 0 30px 0;
  border: 1px solid #093D54;
  cursor: pointer;
  outline: none;
  border-radius: 10px;
}

.download {
  background-color: #fff;
  width: -webkit-fill-available;
  box-shadow: 0px 0px 5px 1px #0000004D inset;
  border-radius: 8px;
  margin: 18px 15px 10px 6px;
  padding: 0 0px 0 0px;
}

.dkfjl {
  padding: 0 0px 0 0px;
  /* border: 1px solid #00000016; */
  border-left: 0;
  border-top: 0;
  border-bottom: 0;
  /* width: 180px; */
}

.boxess {
  display: flex;
  padding: 0 1px 2px 1px;

}

.mbnm {
  /* width: 360px; */
  border: 1px solid #00000016;
  padding: 0 0 0 6px;
  margin: 0px 0px 0px 0px;
  border-top: 0;
  border-left: 0;
  border-right: 0;
}

p.downloadnewtext {
  text-align: center;
  padding: 11px 0 13px 0px;
  margin: 0;
  color: #093D54;
  font-weight: 600;
}

p.viewsnewtext {
  text-align: center;
  padding: 11px 0 13px 0px;
  margin: 0;
  color: #093D54;
  font-weight: 600;
}

p.twothousand {
  margin: 0;
  text-align: center;
  padding: 10px 0 6px 0px;
  font-size: 22px;
  color: #000;
  font-weight: 600;
}

p.TotalDownloads {
  text-align: center;
  font-size: 14px;
  color: #000;
  font-weight: 600;
}

p.RemainingCredits {
  margin: 0;
  text-align: center;
  padding: 10px 0 6px 0px;
  font-size: 22px;
  color: #6DE1A4;
  font-weight: 600;
}

button.WantMoreCredits\? {
  border: 0;
  margin: 3rem 0px 0 7px;
  background-color: #093D54;
  color: #fff;
  font-size: 14px;
  padding: 8px 20px 8px 20px;
  border-radius: 10px;
  cursor: pointer;
  outline: none;
}

.accountdetailspagewidth {
  /* width: 18rem; */
  margin: 0 10px 0 0;
}


.accountdetailspagewidth2 {
  width: 32rem;
}

button.webook {
  position: absolute;
  border: 0;
  background-color: #55C2C3;
  padding: 6px 13px 6px 13px;
  top: 76%;
  color: #fff;
  border-radius: 10px;
  outline: none;
  cursor: pointer;
}


span.closebuttonimg {
  /* background-color: #fff; */
  /* padding: 0px 5px 0px 6px; */
  border-radius: 34px;
  color: grey;
  font-weight: 600;
  position: absolute;
  right: 10px;
  /* top: 24px; */
  cursor: pointer;
  box-shadow: 0 1px 4px rgba(0, 0, 0, .16);
  font-size: 12px;
}

p.header-popup {
  font-size: 16px;
  padding: 10px 0px 0 11px;
  color: #093D54;
  font-weight: 600;
  margin: 0;
}

hr.header-bottom-line {
  margin: 6px 0px 9px 12px;
  border: 1px solid #093D54;
  color: #093D54;
  width: 30px;
}

.grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  grid-gap: 0px;
  width: 740px;
}

.grid.one {
  display: flex;
  place-items: center;
  height: 50px;
  font-weight: bold;
}

.one {
  /* background-color: #1abc9c; */
  border-right: 1px solid #00000016;
}

.one-one {
  /* background-color: #1abc9c; */
  border-right: 1px solid #00000016;
}

.two {
  /* background-color: #27ae60; */
  /* border-right: 0; */
  border-right: 1px solid #00000016;

}

.two-two {
  /* background-color: #27ae60; */
  /* border-right: 0; */
  border-right: 1px solid #00000016;

}

.three {
  /* background-color: #f39c12; */
}

.three-three {
  /* background-color: #f39c12; */
  border-right: 1px solid #00000016;

}

.four {
  /* background-color: #f39c12; */
  border-right: 1px solid #00000016;

}

.four-four {
  /* background-color: #f39c12; */
  border-right: 1px solid #00000016;

}


.grey-border {
  border: 1px solid #DDDDDD;
  margin: 20px 10px 10px 10px;
  border-radius: 5px;
  padding: 10px 10px 0 10px;
}

button.Generate-key {
  border: 0;
  background-color: #093D54;
  color: #F6F6F6;
  font-size: 14px;
  padding: 2px 20px 2px 20px;
  border-radius: 11px;
  outline: none;
  cursor: pointer;
}

.white-box-api {
  background-color: #fff;
  padding: 1px 0 3px 0;
  border-radius: 15px;
  margin: 0 0 10px 0;
}

.hidden-api {
  color: #55c2c3;
  font-size: 14px;
  margin: 0px 0;
  padding: 0 0 0 15px;
}

span.popup-copy-api {
  font-size: 12px;
  margin: 0;
  padding: 7px 15px 7px 15px;
  background-color: #EEEEEE;
  border-radius: 20px;
  cursor: copy;
  color: #093D54;
}

span.hid-show-eye-icon {
  padding: 0 10px 0 0px;
  cursor: pointer;
}

span.popup-url {
  font-size: 14px;
  padding: 10px 10px 10px 0px;
  color: #000;
  font-weight: 600;
}

.utl-entitys {
  text-align: left;
  margin: 3rem 0 10px 12px;
  display: flex;
}

.popup-placeholder {
  width: 270px;
  margin: 0 0 0 22px;
  outline: none;
  border: 1px solid #C9C9C9;
  border-radius: 5px;
  padding: 4px 0 4px 10px;
}

input.popup-placeholder::placeholder {
  color: #C2C8CE;
  font-family: 'Lato';
  font-size: 14px;
}

button.edit-button {
  margin: 5px 0 0 2px;
  border: 0;
  font-size: 26px;
  background-color: transparent;
  color: #093d54;
  padding: 0px 6px 0px 5px;
  border-radius: 4px;
  outline: none;
  cursor: pointer;
  position: absolute;
  right: 29px;
}

img.edit-image {
  position: absolute;
  right: 30px;
  margin: 1px 0 0px 0;
  cursor: pointer;
  border-radius: 3px;
}

span.close-eye {
  position: absolute;
  right: 32%;
  margin: 1px 8px 0 10px;
  background-color: #fff;
}

.status-entitys {
  text-align: left;
  padding: 10px 0 10px 11px;
}

span.status-url {
  font-size: 14px;
  margin: 0 1rem 0px 0px;
  color: #000;
  font-weight: 600;
}

span.active-api-radio {
  padding: 0 10px 0 5px;
  color: #55C2C3;
}

span.inactive-api-radio {
  padding: 0 10px 0 5px;
  color: #C9C9C9;
}


button.api-popup-submit {
  position: absolute;
  right: 25px;
  border: 0;
  background-color: #093D54;
  padding: 4px 26px 4px 26px;
  color: #fff;
  font-size: 14px;
  border-radius: 8px;
  outline: none;
  cursor: pointer;
}

.x-scroll {
  width: 220px;
  overflow-x: scroll;
  white-space: nowrap;
}

::-webkit-scrollbar-track {
  background-color: #ebebeb;
  -webkit-border-radius: 10px;
  border-radius: 10px;
}

input.input-edited {
  border: 0;
  background-color: transparent;
  font-size: 14px;
  outline: none;
  width: 1000px;
}

input.input-edited::placeholder {
  font-weight: 600;
  color: #C9C9C9;
}

::-webkit-scrollbar {
  height: 5px;
}

.scrollbar-width-thin {
  scrollbar-width: thin;
}

p.webbooksecretkey {
  font-weight: 600;
  font-size: 14px;
}



.main-card {
  display: grid;
  grid-template-columns: 1fr 1fr auto;
  gap: 0px;
  background: #fff;
  padding: 0px;
  border-radius: 10px;
  box-shadow: inset 0 0 5px 1px #0000004d;
  width: 100%;
  max-width: none;
  margin: 20px 22px 0 22px;
}

.sub-card {
  display: grid;
  grid-template-columns: 1fr 1fr auto;
  gap: 0px;
  padding: 0px;
  border-radius: 0;
  border-right: 1px solid #00000016;
}

.stats{
  display: grid;
  grid-template-columns: 1fr 1fr auto;
  gap: 0px;
  padding: 0px;
  border-radius: 0;
  border-right: 1px solid #00000016;
}


.section:last-child {
  border-right: none;
  padding-right: 0;
}

.stats div {
  text-align: center;
}

.number {
  font-size: 24px;
  font-weight: bold;
  margin: 0;
}

.number.highlight {
  color: #4caf50;
}

.button-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 20px 0 20px;
}

button.WantMoreCredits {
  background-color: #093D54;
  color: #fff;
  border: none;
  border-radius: 10px;
  padding: 10px 20px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}



@media (max-width: 768px) {
  .card {
      grid-template-columns: 1fr;
      gap: 10px;
  }

  .section {
      border-right: none;
      padding-right: 0;
  }

  .stats {
      flex-direction: column;
      align-items: center;
  }
}

.section.downloads h3 {
  border-right: 1px solid #00000016;
  margin: 0;
  padding: 13px 0 13px 0;
  border-bottom: 1px solid #00000016;
  color: #093D54;
  font-weight: 600;
  text-align: center;
  font-size: 18px;
}

.section.views h3{
  border-right: 1px solid #00000016;
  margin: 0;
  padding: 13px 0 13px 0;
  border-bottom: 1px solid #00000016;
  color: #093D54;
  font-weight: 600;
  text-align: center;
  font-size: 18px;
}


p.colored-number {
  text-align: center;
  font-size: 24px;
  font-weight: 600;
  margin: 14px 0 0 0;
}

p.number-highlight {
  margin: 14px 0 0 0;
  text-align: center;
  font-size: 24px;
  color: #6DE1A4;
  font-weight: 600;
}
