@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400&display=swap');


body {
    font-family: 'Poppins', sans-serif;
    font-weight: 400;
    /* Regular */
}
.checkbox-label-text {
  margin-left: 12px;
  margin: -4px 0 0 20px;
  width: 100;
  /* Adjust the spacing between the checkbox and the text */
}

/* Hover styles for the label text */
.form-controller:hover .hideText {
  display: block;
  font-size: 10px;
  z-index: 1000;
}

/* .hideText {
  position: absolute;
  margin: -4px 0 0 24px;
  background-color: #fff;
  border: 1px solid red;
  padding: 3px 10px 3px 10px;
  border-radius: 4px;
} */

/* .hideText {
  margin: -2px 0 0 24px;
  background-color: #fff;
  border: 1px solid red;
  padding: 3px 10px 2px 10px;
  border-radius: 4px;
  position: absolute;
  z-index: 100;
} */

/* .hideText {
  display: none;
} */







body {
  overflow-x: hidden;
}

.search-result p {
  color: #7e8c9c;
  padding: 10px 0 0 0px;
  font-size: 14px;
  -webkit-text-stroke-width: thin;
}

span.number-of {
  font-weight: 700;
  color: #000;
}

table {
  border-collapse: separate;

  border-spacing: 0 5px;

  width: 100%;
}

.download-all p {
  color: #55c2c3;
  font-size: 14px;
  padding: 8px 0 0;
}

span.view {
  padding: 0 8px 0 20px;
  color: #c2c8ce;
  font-size: 14px;
}

span.downlod-img img {
  max-width: 20px;
  height: 20px;
  margin-right: 0px;
  cursor: pointer;
}

.company-table-box {
  background-color: #ebf6ff;
  padding: 11px 0 11px 10px;
}

span.mail- {
  font-size: 12px;
  color: #000000;
}

.col {
  padding: 0px 0 0 15px;
}

span.telephone {
  padding: 0 0 0 15px;
}

span.call- {
  font-size: 12px;
  color: #000000;
}

p.Chief {
  margin: 0;
  font-size: 12px;
  padding: 6px 0 0 0px;
  line-height: 13px;
}


.table-data {
  /* padding: 11px 0 11px 10px; */
  box-shadow: inset 0px 0px 4px 3px #afafaf38, 4px 0px 4px 0 #0c20350d;
  margin-top: 11px;
  margin-bottom: 30px;
  border-radius: 6px;
}

.eye {
  margin-left: 22px;
  cursor: pointer;
  padding: 0 18px 0px 0px;
  width: 36px;
}

p.contact-name {
  margin: 0;
  font-weight: 600;
}

input.check-input {
  margin-top: 6px;
}

.delete {
  cursor: pointer;
}

.inviter {
  margin-top: 18px;
  margin-bottom: 37px;
}

.middle {
  width: 65%;
  height: 20px;
  border-bottom: 1px solid #c2c8ce;
  text-align: center;
  margin: auto;
}

.text-content {
  font-size: 20px;
  background-color: #ffffff;
  padding: 0 30px 0 30px;
  color: #7e8c9c;
}

a.ancher {
  text-decoration: none;
  color: #55c2c3;
}

.modal-header1 {
  padding: 5px 8px 5px 0px;
}

label.job-checkbox {
  font-size: 14px;
}

label.label-1 {
  font-size: 14px;
  margin: 0;
  color: #707070;
  font-family: "Lato", Regular;
}

p.Douglas {
  font-size: 15px;
  color: #121212eb;
  margin: 0px 0 6px 0px;
  font-family: "Lato", Regular;
  font-weight: 600;
}

p.hide {
  visibility: hidden;
  margin: -17px;
}

hr.small-horizontal {
  padding: 8px 0 0px 0px;
  margin-top: 3px;
  margin-bottom: 0;
}

.row.align-items-start {
  margin-top: 23px;
}

.social-media {
  padding: 0 5px 0 5px;
}

.download-button {
  text-align: center;
  padding: 14px 0 10px 10px;
  position: inherit;
}

.contacts-selected p {
  margin: 0;
  padding: 15px 0 0 6px;
  font-size: 14px;
  color: #c2c8ce;
}

.download-button button {
  border: 0;
  outline: none;
  font-size: 12px;
  padding: 4px 14px 4px 14px;
  border-radius: 4px;
  background-color: #093d54;
  color: #fff;
  cursor: pointer;
}

span.owners {
  color: #000;
  font-family: Lato, Regular;
  font-size: 12px;
  padding: 0 0 0 7px;
  font-weight: normal;
}

.buisness {
  /* padding: 0 0 0 18px; */
  display: contents;
}

.detailing h4 {
  font-size: 16px;
  color: #55c2c3;
  font-family: Lato, Regular;
  padding: 3px 0 0 2px;
  font-weight: 500;
}

.detailing {
  padding: 11px 0 0 24px;
}

hr.hr-detail {
  margin: unset;
  padding: 0 0 1px 1px;
  width: 34px;
  background-color: #55c2c3;
  margin-left: 3px;
}

p.company-name {
  font-size: 13px;
  font-weight: 600;
  color: #000;
  padding: 6px 0px 4px 30px;
  text-align: left;
  text-decoration: none;
  margin: 6px 0 12px 0;
  font-family: Lato, Regular;
  width: 136px;
}

p.company-name a {
  text-decoration: none;
  color: #000;
}

span.aj-builders {
  padding: 0 0 0 30px;
  font-size: 14px;
  color: #093d54;
}

footer.main-footer {
  padding: 0 0 0 16px;
  background-color: transparent;
}

p.company-name1 a {
  font-size: 14px;
  margin: 0;
  text-decoration: none;
  color: #093d54;
  display: flex;
  font-family: Lato, Regular;
  width: 180px;
  word-break: break-all;
}

p.company-name1 {
  padding: 0;
  margin: 13px 22px 7px 26px;
  font-weight: 600;
}

/* .vl {
  border-left: 1px solid #c8c6c4;
  height: auto;
  margin-left: 41px;
  margin-bottom: 6px;
} */

th.companyheaders {
  padding: 14px 5px 14px 0px;
  font-size: 15px;
  font-family: "Lato", Regular;
  white-space: pre-line;

}

th.section-1 {
  padding: 13px 0 13px 10px;
  font-family: "Lato", Regular;
}

th.section-2 {
  padding: 14px 0px 14px 10px;
  font-size: 16px;
  font-family: "Lato", Regular;
  /* width: 148px; */
}

th.company-name {
  padding: 14px 14px 14px 10px;
  font-size: 16px;
  font-family: "Lato", Regular;
  /* width: 148px; */
}

td.section-3 {
  padding: 14px 0px 14px 12px;
  font-family: "Lato", Regular;
  width: 50px;
}

td.section-5 {
  width: 200px;
}

td.section-4 {
  font-family: 'Poppins', sans-serif;
  font-size: 14px;
  margin-top: 16px;
  padding: 10px 2px 10px 0px;
  word-wrap: break-word;
}

td.section-5 a {
  font-family: Lato, Regular;
  font-size: 13px;
  margin-top: 16px;
  font-weight: 600;
  padding: 6px 5px 6px 0px;
  max-width: 25px;
  word-wrap: break-word;
  color: #55C2C3;
  text-decoration: underline;
}

.table-space {
  padding: 4px 0 3px 0px;
}

.download-all {
  text-align: end;
}

.download-all {
  text-align: end;
  padding: 1px 10px 0px 0px;
}

/* a.downld-href {
  text-decoration: none;
  color: #55c2c3;
} */

button.view-select {
  border: 0;
  outline: none;
  margin-top: 9px;
  background-color: transparent;
  font-size: 14px;
  cursor: pointer;
  color: #a3a3a3;
  padding-top: 1px;
}

button.view-select-active {
  border: 0;
  outline: none;
  margin-top: 0px;
  background-color: transparent;
  font-size: 14px;
  cursor: pointer;
  color: #55c2c3;
}

.view-select-active {
  border: 0;
  outline: none;
  margin-top: 9px;
  background-color: transparent;
  font-size: 14px;
  cursor: pointer;
  color: #55c2c3;
}

button.account {
  background-color: #fff;
  outline: none;
  border: 0;
  cursor: pointer;
  padding: 6px 0 6px 0;
}

span.phone-call {
  padding: 0 5px 0 15px;
}

.buisness p {
  margin: 5px 0px 6px 0;
  width: 250px;
}

/* .back-color {
  background-color: #f6faff;
} */

.dropdown-divider {
  height: 0;
  margin: 0px 0 0px 0;
  overflow: hidden;
  border-top: 1px solid #dcdcdc61;
}

.dropdown-dividerr {
  height: 0;
  margin: 0px 5px 0px 5px;
  overflow: hidden;
  border-top: 1px solid #dcdcdc61;
}



.col-md-3.menu {
  padding: 0px 0px 0 19px;
}

.main-footer {
  padding: 0 0px 0 16px;
}

.extra-border {
  /* border: 1px solid #e1e1e1; */
  box-shadow: 0px -3px 4px #0c20350d;
  margin-top: 4px;
}

.greenBlueShade {
  color: #55c2c3 !important;
}

.form-controller {
  font-family: system-ui, sans-serif;
  /* font-size: 2rem; */
  font-weight: bold;
  /* line-height: 1.1; */
  display: grid;
  grid-template-columns: 1em auto;
  gap: 0.5em;
  margin: 0;
  padding: 0;
}

input[type="checkbox"] {
  appearance: none;
  background-color: #fff;
  margin: 0;
  font: inherit;
  color: currentColor;
  width: 16px;
  height: 16px;
  border: 1px solid currentColor;
  border-radius: 2px;
  transform: translateY(-0.075em);
  margin-right: 7px;
  display: inline-flex;
  place-content: center;
  border: 1px solid #BFBFBF;
  cursor: pointer;
}

input[type="checkbox"]::before {
  content: "";
  width: 11px;
  height: 13px;
  transform: scale(0);
  transition: 120ms transform ease-in-out;
  box-shadow: inset 1em 1em var(--form-control-color);
  background-color: #55c2c3;

  transform-origin: bottom left;
  clip-path: polygon(19% 56%, 0 62%, 53% 101%, 97% 23%, 86% 13%, 48% 74%);
}

input[type="checkbox"]:checked::before {
  transform: scale(1);
  border: 1px solid pink;
  /* background-color: CanvasText; */
}

input[type="checkbox"]:checked:focus {
  border-color: #55c2c3;
}

.form-control+.form-control {
  margin-top: 1em;
}

input:checked {
  border: 1px solid #55c2c3;
  accent-color: cadetblue;
}

a.downld-href {
  text-decoration: none;
  color: #fff;
  background-color: #093d54;
  padding: 5px 10px 5px 10px;
  border-radius: 7px;
  font-size: 14px;
}

.d-image {
  padding: 0 0 0 11px;
  position: inherit;
}

span.dlnld {
  padding: 3px 6px 0 8px;
}

span.cancela {
  margin-left: 2rem;
}

span.cancela img {
  border-radius: 34px;
  box-shadow: rgb(100 100 111 / 20%) 0px 7px 29px 0px;
}


/* 23-08-2023 */


.sortbutton {
  background-color: #fff;
  border: 1px solid #7E8C9C;
  padding: 5px 22px 5px 12px;
  border-radius: 10px;
  margin: 3px 8px 0 0px;
  color: #7E8C9C;
  font-size: 14px;
}

p.clearfilter {
  margin: 0px 0 6px 0;
}

.dropdown-toggle::after {
  display: inline-block;
  width: 0;
  height: 0;
  margin-left: 4rem;
  vertical-align: inherit;
  content: "";
  border-top: 0.3em solid;
  border-right: 0.3em solid transparent;
  border-bottom: 0;
  border-left: 0.3em solid transparent;
}

.dropdown-menu.show {
  min-width: 10rem;
  padding: 4px;
  margin: 5px 0 0 2px;
  border-radius: 9px;
  box-shadow: rgb(60 64 67 / 30%) 0px 1px 2px 0px, rgb(60 64 67 / 15%) 0px 2px 6px 2px;
  border: 0;
}

.dropdown-item:focus,
.dropdown-item:hover {
  color: #16181b;
  text-decoration: none;
  background-color: transparent;
}

/* a.dropdown-item:hover {
  background-color: #fff;
  color: #000;
} */

.dropdown-item {
  display: block;
  width: 100%;
  padding: 5px 15px 0 15px;
  clear: both;
  font-weight: 400;
  color: #212529;
  text-align: inherit;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
  font-size: 13px;
}





/* CompanyPopup */

.hrnew {
  border-bottom: 2px solid #093d54;
  width: 46px;
  margin: 0 0 0 50px;
  display: inline-table;
  margin-bottom: 2px;
  color: #093d54;
}

.detailed-company {
  padding: 0 1rem 0 0rem;
}

.detailsheader {
  font-size: 1em !important;
  color: #55C2C3;
}

button.newdownload {
  border: 0;
  background-color: #093d54;
  color: #fff;
  font-size: 14px;
  padding: 3px 10px 3px 10px;
  border-radius: 5px;
}

p.Cname a {
  font-size: 14px;
  color: #000;
  text-decoration: none;
}

@media (max-width:991px) {
  .footer-menu {
    padding-left: 0
  }
}

li.clist {
  list-style: none;
  font-size: 14px;
}

ul.culist {
  margin: 0;
  padding: 0;
}

p.cname {
  width: 150px;
  color: #000;
  font-size: 14px;
  padding: 0 0px 12px 0px;
}

p.lname {
  font-size: 14px;
  color: #093d54;
}

.cdetailed {
  padding: 1rem 1rem 1rem 1rem;
}



/* Reset default button styles */
/* button.view-select {
  border: none;
  padding: 10px 20px px;
  background-color: transparent;
  cursor: pointer;
  margin-top: 2px;
  font-size: 14px;
} */

/* Style for normal state */
.custom-button {
  color: #333;
  border: 2px solid #333;
}

/* Style for active state */



.search-result {
  margin: 0;
  padding: 0px 0 0px 0px;
  color: #7E8C9C;
  font-size: 14px;
}

/* 24-082023 */


a.dropdown-item {
  font-size: 14px;
  padding: 4px 0px 12px 14px;
}

.table td,
.table th {
  /* padding: 16px 10px 15px 23px; */
  /* vertical-align: top; */
  border-top: 1px solid #dee2e6;
  font-weight: 600;
  /* max-width: 80px; */
}

tr.free-trial-table-header {
  background-color: #55C2C3;
}

th span.doller {
  background-color: #fff;
  font-size: 24px;
  margin: auto;
  display: block;
  text-align: center;
  width: 85px;
  position: relative;
  border-radius: 15px;
  padding: 3px 0 3px 0;
  right: 8px;
  /* top: -3px; */
}

th .aap-sumo-doller {
  background-color: #fff;
  font-size: 24px;
  margin: auto;
  display: block;
  text-align: center;
  width: 85px;
  position: relative;
  border-radius: 15px;
  padding: 3px 0 3px 0;
  right: 8px;
  top: -3px;

}

/* th .super-saver-doller{
  background-color: #fff;
  font-size: 24px;
  margin: auto;
  display: block;
  text-align: center;
  width: 85px;
  position: relative;
  border-radius: 15px;
  padding: 3px 0 3px 0;
  right: 8px;
  top: -3px;
} */

th .super-saver-doller {
  background-color: #fff;
  font-size: 24px;
  /* margin: auto; */
  display: block;
  text-align: center;
  width: 85px;
  /* position: absolute; */
  border-radius: 15px;
  padding: 3px 0 3px 0;
  right: 11%;
  top: 20%;
}

th span.dolllers {
  background-color: #fff;
  font-size: 24px;
  /* position: absolute; */
  margin: auto;
  display: block;
  text-align: center;
  width: 85px;
  position: relative;
  bottom: 2px;
  border-radius: 15px;
  padding: 3px 0 3px 0;
  right: 8px;
}

.first {
  display: inline-block;
}

/* .checkboxstyle[type="checkbox"][id^="checkbox"] {
  display: none;
} */

.first {
  /* padding: 10px; */
  display: block;
  position: relative;
  /* margin: 10px; */
  cursor: pointer;
}

p.rotate {
  margin: 0;
}

.first p {
  text-align: center;
  font-family: Open Sans;
  font-weight: 700;
}

.first img {
  height: 10px;
  width: 10px;
  /* transition-duration: 0.2s; */
  position: absolute;
  left: 3px;
  margin-top: 5px;
}

:checked+.first:before {
  transform: scale(2);
}

:checked+.first img {
  transform: scale(0.9);
  box-shadow: inset 0 -3em 3em rgba(0, 0, 0, 0.1),
    0 0 0 2px rgb(255, 255, 255),
    0.3em 0.3em 1em rgba(0, 0, 0, 0.3);

}





/* span.hideText::after {
  content: "";
  position: absolute;
  top: 7px;
  left: -4px;
  border-top: 3px solid transparent;
  border-right: 4px solid #ffffff;
  border-bottom: 3px solid transparent;
  z-index: -1;
  background-color: red;
  border-radius: 230px;
} */


label.second {
  margin: 0;
  padding: 0;
}

.back-color {
  background-color: #fff;
}


.vl {
  border: 1px solid #C8C6C4;
  margin-top: 5px;
}


.cncl {
  margin: -6px 11px 0 30px;
}

button.sortby {
  border: 1px solid #7E8C9C;
  margin: 0px 8px 10px 0px;
  padding: 4px 10px 4px 10px;
  background-color: #ffff;
  border-radius: 10px;
  outline: none;
  box-shadow: inset 0px 0px 2px 0px #7E8C9C;
  font-size: 14px;
  color: #7E8C9C;
  cursor: pointer;
}

.trey-one {
  border: 1px solid #7E8C9C;
  margin: 6px 0 0 0;
  padding: 10px 0 0 0;
  border-radius: 20px;
}

button.sortby img {
  margin: 0 10px 0 0px;
}

ul.left {
  position: absolute;
  background-color: #fff;
  z-index: 1000;
  padding: 3px 10px 8px 19px;
  margin: 6px 0 0 0;
  border-radius: 8px;
  box-shadow: rgb(0 0 0 / 10%) 0px 4px 12px;
  cursor: pointer;
}

span.caret-img {
  padding: 0 0 0 3rem;
}

ul.left li {
  list-style: none;
  padding: 5px 0px 6px 0px;
  font-size: 14px;
  margin: 2px 0px 0 0;
}

.sub-drop-1 {
  position: absolute;
  background-color: #fff;
  z-index: 1;
  left: 12rem;
  top: 10px;
  box-shadow: rgb(0 0 0 / 10%) 0px 4px 12px;
  width: max-content;
  padding: 0px 20px 0 20px;
  border-radius: 10px;
}

.sub-drop-2 {
  position: absolute;
  left: 12rem;
  top: 3rem;
  background-color: #fff;
  padding: 0px 1rem 0px 1rem;
  border-radius: 10px;
  box-shadow: rgb(0 0 0 / 10%) 0px 4px 12px;
}

.months p {
  padding: 0 0 0 0;
  margin: 7px 2rem 7px 0px;
  font-size: 14px;
}

.radio {
  padding: 7px 0 7px 0;
}

/*
.right{
  position: absolute;
  left: 10rem;
  background-color: #fff;
  padding: 11px 17px 15px 18px;
  margin: 5px 0 0 0;
  width: max-content;
} */

.fixTableHead {
  overflow-y: auto;
  height: 100vh;
  z-index: 1;
}

.fixTableHeadcompany {
  overflow-y: auto;
  height: 100vh;
  z-index: 1;
}

.fixTableHead .table-box th {
  position: sticky;
  top: 0;
  background-color: #ebf6ff;
  z-index: 1;
  width: 200px;
  font-family: 'Poppins', sans-serif;
  font-weight: 500;

}

.fixTableHeadcompany .table-box th {
  position: sticky;
  top: 0;
  background-color: #ebf6ff;
  z-index: 1;
  /* width: 200px; */
}


.d-image img {
  opacity: 0.5;
}


.selected-items-bar {
  background-color: #FAFAFA;
  padding: 10px 0 10px 15px;
}

input.dropcheck {
  margin: 8px 4px 0 0;
}

.droppose {
  position: absolute;
  left: -37px;
  top: 7px;
}

a.dropdown-item.Selectthis {
  font-size: 14px;
  padding: 12px 0 12px 28px;
  color: #093D54;
  font-weight: 600;
  cursor: pointer;
  width: 166px;
}

button.addtolist {
  background-color: #093D54;
  border: 0;
  color: #fff;
  font-size: 12px;
  padding: 5px 11px 4px 11px;
  border-radius: 5px;
  cursor: pointer;
  outline: none;
  opacity: 0.3;
}

button.savedlist {
  background-color: #093D54;
  border: 0;
  color: #fff;
  font-size: 12px;
  padding: 4px 11px 4px 11px;
  border-radius: 5px;
  cursor: pointer;
  outline: none;
}

button.savedlist1 {
  background-color: #55C2C3;
  border: 0;
  color: #fff;
  font-size: 12px;
  padding: 5px 11px 4px 11px;
  border-radius: 5px;
  cursor: pointer;
  outline: none;
}

p.oSelected {
  margin: 0;
  color: #000;
  font-size: 14px;
  padding: 4px 0 0 9px;
}

.dropdown-content {
  position: absolute;
  z-index: 1000;
  background-color: #fff;
  width: 300px;
  left: -45px;
  border-radius: 10px;
  box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
  margin: 16px 0 0 0;
}

button.caretdrop {
  border: 0;
  padding: 0;
  outline: none;
  margin: 4px 0 0 -1px;
  background-color: unset;
}

button.caretdrop img {
  margin: -4px 0 0 0;
  cursor: pointer;
}

.box.arrow-top2:after {
  content: " ";
  position: absolute;
  right: 9px;
  top: -11px;
  border-top: none;
  border-right: 8px solid #91818100;
  border-left: 9px solid transparent;
  border-bottom: 11px solid white;
}

input.miniinput {
  border-radius: 6px;
  outline: none;
  border: 1px solid #bdbdbd;
  width: 70px;
  height: 22px;
  cursor: pointer;
}

input.miniinputt {
  border-radius: 6px;
  outline: none;
  border: 1px solid #bdbdbd;
  width: 65px;
  height: 22px;
  cursor: pointer;
  margin: 4px 0 0 4px;
}

p.max-contacts {
  font-size: 10px;
  text-align: center;
  color: #7E8C9C;
}

p.miniselect {
  margin: 0;
  color: #000;
  font-size: 14px;
  padding: 5px 0 0 0;
}

input.frominput {
  border-radius: 5px;
  border: 1px solid #b0b0b0;
  width: 50px;
  height: 20px;
  cursor: pointer;
}

.inputdropdown {
  width: 226px;
  margin: 0 0 0 2rem;
  border-radius: 10px;
  box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
  padding: 10px 0 10px 0;
}

button.selectcontactsubmitbutton {
  margin: auto;
  display: block;
  border: 0;
  padding: 5px 24px 5px 24px;
  border-radius: 11px;
  font-size: 14px;
  background-color: #093D54;
  color: #fff;
  cursor: pointer;
}

button.selectapplybutton {
  margin: 7px 0 15px 20px;
  display: block;
  border: 0;
  padding: 5px 24px 5px 24px;
  border-radius: 11px;
  font-size: 14px;
  background-color: #093D54;
  color: #fff;
  cursor: pointer;
  opacity: 0.3;
}

button.selectapplybutton-enable {
  margin: 16px 0 15px 0px;
  display: block;
  border: 0;
  padding: 5px 24px 5px 24px;
  border-radius: 11px;
  font-size: 14px;
  background-color: #093D54;
  color: #fff;
  cursor: pointer;
  outline: none;
}

p.ClearSection {
  margin: 10px 0 10px 20px;
  font-size: 14px;
  color: #55C2C3;
}

span.addition {
  font-size: 14px;
  padding: 0;
  margin: 0px;
  cursor: pointer;
}

span.savecontactsunderline {
  border-bottom: 2px solid #093d54;
  color: #093d54;
  display: inline-table;
  position: absolute;
  width: 40px;
  margin: 32px 0 0 0px;
}

h5#exampleModalLabel {
  font-size: 16px;
  font-weight: 600;
  color: #093D54;
}

p.AddToNewList {
  font-size: 12px;
  color: #000;
  font-weight: 600;
  margin: 0 0 8px 0;
}

input.AddToNewListinput {
  width: 100%;
  border: 1px solid #C9C9C9;
  border-radius: 4px;
  padding: 3px 0 3px 6px;
  font-size: 14px;
  outline: none;
}

input.AddToNewListinput::placeholder {
  color: #C9C9C9;
  font-size: 12px;
}

button.cancel {
  border: 1px solid #093D54;
  background-color: white;
  font-size: 14px;
  padding: 5px 25px 5px 25px;
  border-radius: 10px;
  color: #093D54;
  font-weight: 600;
  cursor: pointer;
  margin: 0 0 0 15px;
  opacity: 0.3;
  outline: none;
}

button.Savepopup {
  border: 1px solid #093D54;
  font-size: 14px;
  color: #fff;
  background-color: #093D54;
  padding: 5px 30px 5px 30px;
  border-radius: 10px;
  cursor: pointer;
  opacity: 0.3;
  outline: none;
}

p.AddToYourLists {
  font-size: 12px;
  color: #000000;
  font-weight: 600;
  margin: 10px 0 0 0;
}

ul.unorderedlist {
  list-style: none;
  padding: 0;
}

li.orderedlist {
  font-size: 14px;
  color: #000;
}

.fleayerbox {
  border: 1px solid #C9C9C9;
  border-radius: 4px;
  margin: 0 5px 10px 0;
  background-color: #fff;
}

p.ContactList1 {
  padding: 6px 0px 6px 15px;
  margin: 0;
  color: #000000;
  font-weight: 600;
}

.fixpopuphead {
  height: 140px;
  overflow-y: auto;
  z-index: 1;
}

span.successmessage3 {
  border: 1px solid #6DE1A4;
  font-size: 10px;
  padding: 0px 18px 0px 3px;
  border-radius: 8px;
  margin: 4px 0 0px 36px;
  display: block;
  position: absolute;
  background-color: #EEFFF6;
}

span.successmessage3 img {
  margin: 4px 0 0 0px;
}

.dropdown-content::after {
  content: " ";
  position: absolute;
  top: -21px;
  border-top: 7px solid transparent;
  border-right: 10px solid transparent;
  border-left: 10px solid transparent;
  border-bottom: 24px solid white;
  left: 22px;
}

.inputdropdown::after {
  content: " ";
  position: absolute;
  top: -21px;
  border-top: 7px solid transparent;
  border-right: 10px solid transparent;
  border-left: 10px solid transparent;
  border-bottom: 24px solid white;
  left: 22px;
}

button.RevealEmail {
  background-color: #F5F5F5;
  border: 0;
  font-size: 12px;
  padding: 4px 8px 4px 8px;
  border-radius: 5px;
  outline: none;
  box-shadow: inset 0 0 4px #00000029;
  cursor: pointer;
  margin: 0 0 0 0px;
  color: #000;
}

button.RevealEmailmodel {
  background-color: #F5F5F5;
  border: 0;
  font-size: 12px;
  padding: 4px 8px 4px 8px;
  border-radius: 5px;
  outline: none;
  box-shadow: inset 0 0 4px #00000029;
  cursor: pointer;
  margin: 0 0 0 10px;
  color: #000;
}


button.RevealEmailnow {
  background-color: #F5F5F5;
  border: 0;
  font-size: 12px;
  padding: 4px 8px 4px 8px;
  border-radius: 5px;
  outline: none;
  box-shadow: inset 0 0 4px #00000029;
  cursor: pointer;
  margin: 0 0 0 10px;
}

button.Verifyingemail {
  background-color: #fff;
  border: 1px solid #B7B7B7;
  padding: 2px 10px 2px 10px;
  border-radius: 5px;
  cursor: pointer;
  color: #B7B7B7;
  outline: none;
}

button.Verifyingemail-contact {
  background-color: #fff;
  border: 1px solid #B7B7B7;
  padding: 2px 10px 2px 10px;
  border-radius: 5px;
  cursor: pointer;
  color: #B7B7B7;
  outline: none;
  margin: 0 0 0 6px;
  font-size: 12px;
}

button.Verifyingemail1 {
  background-color: #fff;
  border: 1px solid #B7B7B7;
  padding: 2px 10px 2px 10px;
  border-radius: 5px;
  cursor: pointer;
  color: #B7B7B7;
  outline: none;
  margin: 0 0 0 10px;
}

span.percentage {
  color: #000000;
  font-weight: 600;
  font-size: 12px;
  padding: 0 0 0 5px;
}

.verifiedemailleayerone {
  border: 1px solid #D4D8DD;
  padding: 5px 5px 7px 5px;
  border-radius: 8px;
  position: absolute;
  left: 27%;
  cursor: pointer;
  background-color: #fff;
  width: 200px;
}

.verifiedemailleayerone2 {
  border: 1px solid #6DE1A4;
  padding: 0 0 6px 10px;
  border-radius: 6px;
  background-color: #EEFFF6;

}

p.douglas45gmailcom {
  margin: 0 0 0px 10px;
  color: #55C2C3;
  font-weight: 300;
  cursor: pointer;
  font-size: 10px;
  padding: 2px 0px 0 0px;
}


small.Businessemail {
  color: #D4D8DD;
  padding: 0 0 0 10px;
}

small.Thisemailisverifiedandvalid {
  padding: 0 6px 0 0px;
}

p.VerifiedEmail2 {
  margin: 4px 0 0 0;
  color: #6DE1A4;
  font-weight: 400;
}


.trevealemail {
  border: 1px solid #E1EDF5;
  font-size: 12px;
  margin: 2px 6px 3px 6px;
  padding: 2px 6px 2px 6px;
  border-radius: 4px;
  background-color: #F5F5F5;
  outline: none;
  cursor: pointer;
}

.contactverifybutton {
  border: 1px solid #E4E4E4;
  background-color: #fff;
  border-radius: 3px;
}

.contactbackground {
  border: 1px solid #E4E4E4;
  margin: 0 0 0 5px;
  border-radius: 4px;
  padding: 0 7px 0 7px;
}

span.contactverifyingemail {
  padding: 0 5px 0 5px;
  font-size: 12px;
  color: #B7B7B7;
}

span.percentagecontact {
  font-size: 12px;
}

button.contactemailnotfound {
  font-size: 12px;
  color: #DE350B;
  border: 1px solid #DE350B;
  margin: 0 0 0 5px;
  border-radius: 4px;
  background-color: #fff;
}


.contactrevealemail {
  font-size: 12px;
  margin: 0 0 0 5px;
  border: 1px solid #00000029;
  border-radius: 4px;
  background-color: #F5F5F5;
  outline: none;
  cursor: pointer;
}

button.dashboardEmailNotfound {
  font-size: 12px;
  border: 1px solid #E4E4E4;
  padding: 3px 8px 3px 8px;
  border-radius: 4px;
  color: #B7B7B7;
  background-color: #fff;
  outline: none;
  margin: 0 0 0 8px;
}

button.dashboardEmailNotfound2 {
  font-size: 12px;
  border: 1px solid #E4E4E4;
  padding: 3px 8px 3px 8px;
  border-radius: 4px;
  color: #B7B7B7;
  background-color: #fff;
  outline: none;
  margin: 0 0 0 0px;
}

button.dashboardEmailNotfoundyet {
  font-size: 12px;
  border: 1px solid red;
  padding: 5px 8px 5px 8px;
  border-radius: 4px;
  color: red;
  background-color: #fff;
  outline: none;
  margin: 0 0 0 10px;
}

button.dashboardEmailNotfound12 {
  font-size: 12px;
  border: 1px solid red;
  padding: 5px 8px 5px 8px;
  border-radius: 4px;
  color: red;
  background-color: #fff;
  outline: none;
  margin: 0 0 0 10px;
}

p.dashboardemail {
  color: #55C2C3;
  margin: 0;
  font-weight: 500;
  /* cursor: pointer; */
  word-break: break-word;
  font-size: 12px;
}

/* p.dashboardemaill {
  color: #093D54;
  margin: 4px 0 0 0;
  font-weight: 500;
  cursor: pointer;
  word-break: break-word;
  font-size: 12px;
} */

button.savedlistDownloadbuttonnow {
  font-size: 12px;
  border: 0;
  background-color: #55C2C3;
  color: #fff;
  padding: 2px 40px 2px 40px;
  border-radius: 2px;
}

button.savedlistDownloadbuttonnow-2 {
  font-size: 12px;
  border: 0;
  background-color: #55C2C3;
  color: #fff;
  padding: 2px 17px 2px 17px;
  border-radius: 2px;
}

p.dashboardemaill {
  color: #55C2C3;
  margin: 0;
  font-weight: 500;
  /* cursor: pointer; */
  word-break: break-word;
  font-size: 12px;
  padding: 5px 0 0 0px;
}


.verifiedemailleayerone1 {
  border: 1px solid #D4D8DD;
  padding: px 5px 7px 5px;
  border-radius: 8px;
  position: absolute;
  /* top: 4rem; */
  left: 40px;
  background-color: #fff;
  width: 200px;
  z-index: 1000;
}

.verifiedemailleayerone4 {
  border: 1px solid #D4D8DD;
  padding: px 5px 7px 5px;
  border-radius: 8px;
  position: absolute;
  /* top: 4rem; */
  left: 40px;
  background-color: #fff;
  width: 200px;
}

p.itemcontactname {
  margin: 0px 0px 0 14px;
  padding: 0;
  font-weight: 400;
}


a.dropdown-item.Selectthis:hover {
  color: #093D54;
}


button.companydownload {
  background-color: #093d54;
  color: #fff;
  outline: none;
  border: 0;
  padding: 6px 25px 6px 25px;
  border-radius: 8px;
  vertical-align: middle;
  font-size: 14px;
  margin: 6px 0 0px 0;
  cursor: pointer;
}

img.emailcancelbutton {
  position: absolute;
  top: -10px;
  right: -6px;
  background-color: #fff;
  border: 1px solid #55C2C3;
  padding: 2px 2px 2px 2px;
  border-radius: 20px;
  width: 15px;
  cursor: pointer;
}


p.company-data {
  font-size: 14px;
  font-weight: 600;
  width: max-content;
  margin: 0;
}

th.companyheaders2 {
  font-size: 16px;
  width: 200px;
  padding: 14px 0px 14px 10px;
}

td.company-data {
  font-size: 14px;
  padding: 6px 0 6px 10px;
}

input.check-input {
  padding: 0px 0 0 0;
  margin: 8px 8px 6px 2px;
}

label.company-name-label {
  margin: 0px 0 0 0;
}

td.company-data {
  font-family: Lato, Regular;
  font-size: 12px;
  margin-top: 16px;
  padding: 14px 0 14px 0px;
  word-wrap: break-word;
}


td.industry-1 {
  font-size: 14px;
  width: 200px;
}

td.company-data-filled {
  font-size: 14px;
  /* font-weight: 600; */
  width: 200px;
  padding: 0 0px 0 0px;

}

td.company-data-filledd {
  font-size: 14px;
  font-weight: 600;
}


@media screen and (min-width: 1680px) {
  .fixTableHead {
    overflow-y: auto;
    height: 55rem !important;
    z-index: 1;
  }


}

@media screen and (min-width: 1600px) {
  .fixTableHead {
    overflow-y: auto;
    height: 42rem;
    z-index: 1;
  }

}

@media screen and (min-width: 1440px) {
  .fixTableHead {
    overflow-y: auto;
    height: 52rem;
    z-index: 1;
  }

}

@media screen and (min-width: 1400px) {
  .fixTableHead {
    overflow-y: auto;
    height: 43rem;
    z-index: 1;
  }
}

@media screen and (min-width: 1366px) {
  .fixTableHead {
    overflow-y: auto;
    height: 35rem;
    z-index: 1;
  }
}

@media screen and (min-width: 1920px) {
  .fixTableHead {
    overflow-y: auto;
    height: 53rem;
    z-index: 1;
  }
}


button.close-button {
  border: 1px solid #0000000d;
  border-radius: 26px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, .16);
  cursor: pointer;
  font-size: 14px;
  height: 16px;
  outline: none;
  padding: 0;
  width: 16px;
  background-color: #fff;
  position: absolute;
  right: 10px;
  top: 18px;
}

span.cansl {
  color: #7e8c9c;
  font-size: 14px;
  font-weight: 600;
  position: absolute;
  right: 3px;
  top: -4px;
}


.desend {
  position: absolute;
  right: 31px;
  top: 6rem;
  margin: 3px 0 0 0;
}

.desend2 {
  position: absolute;
  right: 59px;
  top: 8rem;
  margin: 11px 0 0 0;
}

th.company-data-header {
  width: 246px;
}


p.successmessage7 {
  color: #6DE1A4;
  border: 1px solid #6DE1A4;
  font-size: 10px;
  padding: 5px 6px 5px 6px;
  border-radius: 8px;
  margin: 0px 0 0 0;
  display: block;
  position: absolute;
  background-color: #fff;
  width: 110px;
  /* height: 36px; */
  text-align: center;
  position: absolute;
  bottom: -11px;
}

p.successmessage8 {
  color: #6DE1A4;
  border: 1px solid #6DE1A4;
  font-size: 10px;
  padding: 5px 6px 5px 6px;
  border-radius: 8px;
  margin: 0px 0 0 0;
  display: block;
  position: absolute;
  background-color: #fff;
  width: 110px;
  /* height: 36px; */
  text-align: center;
  position: absolute;
  bottom: -2px;
  left: 24px;
}


span.free-trial-doller {
  background-color: #fff;
  border-radius: 20px;
  padding: 6px 37px 5px 19px;
  position: relative;
  margin: 0px 0 0 0px;
  top: -6px;
}

td.text-center {
  padding: 20px 0 0 20px;
}

span.upgrade-doller {
  background-color: #fff;
  padding: 4px 9px 4px 9px;
  border-radius: 20px;
  position: absolute;
  top: 26%;
  right: 11%;
  /* width: 100px; */
}

button.selectapplybutton-enable-contact {
  margin: 7px 0 15px 20px;
  display: block;
  border: 0;
  padding: 5px 24px 5px 24px;
  border-radius: 11px;
  font-size: 14px;
  background-color: #093D54;
  color: #fff;
  cursor: pointer;
  outline: none;
}



/* Reset & Base */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body,
html {
  font-family: 'Poppins', sans-serif;
  height: 100%;
}

/* Layout Wrapper */
.dashboard {
  display: flex;
  min-height: 100vh;
}

/* Sidebar */
.sidebar {
  width: 250px;
  /* background: #1e1e2f; */
  color: #fff;
  padding: 0px;
  position: fixed;
  height: 100vh;
  transition: transform 0.6s ease-in-out;
  z-index: 999;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 5px 0px, rgba(0, 0, 0, 0.06) 0px 0px 0px 0px;
}

.sidebar-header {
  /* display: flex; */
  justify-content: space-between;
  align-items: center;
}

.sidebar ul li a {
  color: white;
  text-decoration: none;
  font-size: 16px;
}

.close-btn {
  font-size: 24px;
  background: none;
  border: none;
  color: #fff;
  cursor: pointer;
  display: none;
}

/* Main Content */
.main-content {
  margin-left: 250px;
  flex: 1;
  background: #f8f8f8;
  padding: 10px;
  transition: margin-left 0.3s ease;
}


/* Mobile Header */
.mobile-header {
  display: none;
  align-items: center;
  background: #1e1e2f;
  color: #fff;
  padding: 10px 20px;
}

.menu-btn {
  font-size: 24px;
  background: none;
  border: none;
  color: #fff;
  margin-right: 15px;
  cursor: pointer;
}

/* Responsive */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    position: fixed;
    left: 0;
    top: 0;
    height: 100%;
  }

  .sidebar.active {
    transform: translateX(0);
  }

  .main-content {
    margin-left: 0;
  }

  .mobile-header {
    display: flex;
  }

  .close-btn {
    display: block;
  }
}


.filter-section-filter {
  position: sticky;
  top: 0;                    
  overflow-y: auto;         
  background-color: #f9f9f9;  
  padding: 0px;
  z-index: 10;
}


.loader-center {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white; /* Optional: can be transparent or semi-transparent */
  z-index: 9999;
}

