@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400&display=swap');


body {
    font-family: 'Poppins', sans-serif;
    font-weight: 400;
    /* Regular */
}

.sidebarv2 {
    background: #f9f9f9;
    width: 240px;
    max-width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    padding: 0px;
    position: relative;
    overflow: auto;
    box-shadow: 0 0 6px #80808029;
    border-right: 1px solid #8080805c;
}

button.Job-Titles-v2 {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 4px 0px;
    font-family: Poppins, sans-serif;
    background-color: #fff;
    border: 0;
    border-radius: 4px;
    outline: none;
    color: #093d54;
    cursor: pointer;
}



.apply-back-v2 {
    background-color: #f5f5f5;
    /* Constant background */
    padding: 12px;
    display: flex;
    justify-content: center;
    position: fixed;
    bottom: 0;
    width: 249px;
    z-index: 999;
    box-shadow: 0 -2px 6px rgba(0, 0, 0, 0.1);
}


/* Button inactive */
.Apply-Filters-button-disable-v2 {
    padding: 5px 30px;
    background-color: #c2c8ce;
    color: #fff;
    border: none;
    border-radius: 5px;
    font-size: 14px;
    font-family: 'Poppins', sans-serif;
}



.search-icon-v2 {
    position: absolute;
    transform: translateY(28%);
    left: 88%;
    margin-top: 0px;
}

/* Button active */
.Apply-Filters-button-v2 {
    padding: 5px 30px;
    background-color: #093d54;
    color: #fff;
    border: none;
    border-radius: 5px;
    font-size: 14px;
    font-family: 'Poppins', sans-serif;
}



.job-checkbox-v2 {
    font-family: 'poppins';
    color: #000;
    font-size: 14px;
}

button.btn.btn-link-v2 {
    border: 0;
    width: -webkit-fill-available;
    text-align: inherit;
    outline: none;
    text-decoration: none;
    color: #093d54;
    padding: 7px 0 7px 8px;
    font-weight: 500;
    font-size: 13px;
    background: transparent;
    font-family: 'poppins';
}