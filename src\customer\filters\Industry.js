import { useContext, useEffect, useState } from "react";
import AsyncSelect from 'react-select/async';
import { PostWithTokenNoCache } from "../common-files/ApiCalls.js";
import { ApiName } from "../common-files/ApiNames";
import DashboardContext from "../common-files/ContextDashboard.js";
import UseTabStore from "../common-files/useGlobalState.js";

const Industry = () => {
  // State to store selected values
  const {
    companyKeyword,
    industryData,
    sicCode,
    companyType,
    companyTypeCheckedBoxes,
    setCurrentPage,
    setSicCode,
    setIndustryData,
    setSelectedCompanyType,
    setCompanyTypeCheckedBoxes,
    setIsFiltered,
    setCompanyKeyword
  } = UseTabStore();

  const { dataDC, setDataDC } = useContext(DashboardContext);

  function handleFilters(name, value) {
    if (name == "company_type") {
      setSelectedCompanyType(value)
    }
  }
  const [inputValue, setInputValue] = useState('');
  const [selectedValues, setSelectedValues] = useState([]);
  const [isCompanyKeywordAutoFocus, setIsCompanyKeywordAutoFocus] = useState(false); // Set one of them to autofocus initially
  const [disableSelect1, setDisableSelect1] = useState(false);
  const [disableSelect2, setDisableSelect2] = useState(false);
  const [isIndustryAutoFocus, setIsIndustryAutoFocus] = useState(false); // Set one of them to autofocus initially
  const [isSICCodeAutoFocus, setIsSICCodeAutoFocus] = useState(false); // Set one of them to autofocus initially
  const [companyTypeOption, setCompanyTypeOption] = useState([
    { "value": "Government Agency", "label": "Government Agency" },
    { "value": "Nonprofit", "label": "Nonprofit" },
    { "value": "Partnership", "label": "Partnership" },
    { "value": "public", "label": "Public" },
    { "value": "Educational", "label": "Educational" },
    { "value": "private", "label": "Private" },
    { "value": "Self-Owned", "label": "Self-Owned" },
    { "value": "Self-Employed", "label": "Self-Employed" }
  ]);
  const [selectedIndustryValues, setSelectedIndustryValues] = useState([]);
  const [selectedSICValues, setSelectedSICValues] = useState([]);

  useEffect(() => {
    if (Object.keys(companyKeyword).length < 1) {
      setSelectedValues([]);
    } else {
      const selectedVal = Object.keys(companyKeyword).map(key => {
        return { label: companyKeyword[key], value: companyKeyword[key] };
      });
      setSelectedValues(selectedVal);
    }
  }, [companyKeyword])

  useEffect(() => {
    if (Object.keys(industryData).length < 1) {
      setSelectedIndustryValues([]);
      setDisableSelect2(false);
    } else {
      setDisableSelect2(true);
      const selectedValues = Object.keys(industryData).map(key => {
        return { label: industryData[key], value: industryData[key] };
      });
      setSelectedIndustryValues(selectedValues);
    }
  }, [industryData])

  useEffect(() => {
    if (Object.keys(sicCode).length < 1) {
      setDisableSelect1(false);
      setSelectedSICValues([]);
    } else {
      setDisableSelect1(true);
      const selectedValues = Object.keys(sicCode).map(key => {
        return { label: sicCode[key], value: sicCode[key] };
      });
      setSelectedSICValues(selectedValues);
    }
  }, [sicCode])

  useEffect(() => {
    //check for selected data and make checkbox true
    const companyTypeValues = Object.values(companyType);
    if (companyTypeValues.length > 0) setCompanyTypeCheckedBoxes(companyTypeValues);
  }, []);

  useEffect(() => {
    //set global state and update filter
    handleFilters("company_type", companyTypeCheckedBoxes);
  }, [companyTypeCheckedBoxes]);

  const noOptionsMessage = () => {
    return inputValue.length < 2 ? 'No options found' : 'No options found';
  };

  const convertToProperCase = async (data) => {
    let properCaseData = [];
    if (data) {
      properCaseData = data.map(val => {
        // Trim the input string to remove leading and trailing whitespace
        val = val.trim();

        // Match words while preserving non-alphanumeric characters in between
        const words = val.match(/\b\w+\b/g);
        let result = "";
        let lastIndex = 0;

        words.forEach(word => {
          // Find the position of each word in the original string
          const start = val.indexOf(word, lastIndex);

          // Append special characters or spaces before the current word
          result += val.slice(lastIndex, start);

          // Capitalize the word
          result += word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();

          // Update lastIndex to the end of this word
          lastIndex = start + word.length;
        });

        // Append any remaining special characters or spaces after the last word
        result += val.slice(lastIndex);

        return result;
      });
    }
    return properCaseData;
  };

  const SearchIndustrys = ({ autoFocus, onFocus, onBlur }) => {

    const loadOptions = (inputText, callback) => {
      if (inputText.length < 1) {
        callback([]);
        return;
      }
      if (inputText && inputText.length > 0) {
        const dataPost = {
          company_industry_categories_list: inputText // Pass the inputText to your API request
        };
        // Make an API call here to fetch suggestions based on the inputText
        PostWithTokenNoCache(ApiName.esAutoSuggestions, dataPost)
          .then(async (res) => {
            if (res.data.status === 200) {
              const industry = JSON.parse(res.data.data);

              let properCaseIndustry = await convertToProperCase(industry);

              // Convert the array to a Set to remove duplicates and then convert it back to an array
              const uniqueIndustry = [...new Set(properCaseIndustry)];

              const options = uniqueIndustry.map((option) => ({
                label: option,
                value: option
              }));
              callback(options);
            }
          })
          .catch((error) => {
            console.error('Error fetching data:', error);
            callback([]); // Clear options on error
          });
      }
    };

    // Handle value change when an option is selected or deselected
    const handleValueChange = (selectedOptions) => {
      setDisableSelect2(true);
      setIsFiltered(true);
      setSelectedIndustryValues(selectedOptions);
      let updatedIndustry = {};

      // Update the company names based on selected options
      selectedOptions.forEach((item, index) => {
        updatedIndustry[index] = item.value;
      });
      setIndustryData(updatedIndustry);
    };

    return (
      <div className="mb-2 mt-3 d-flex flex-column">
        <div className="">
          <AsyncSelect
            cacheOptions
            defaultOptions={false}
            loadOptions={loadOptions}
            isMulti
            placeholder="Search Industries"
            onChange={handleValueChange}
            value={selectedIndustryValues}
            styles={SearchJobtitleStyles}
            autoFocus={autoFocus}
            onFocus={onFocus}
            onBlur={onBlur}
            isClearable
            isDisabled={disableSelect1} // Use the isDisabled prop for disabling
            noOptionsMessage={noOptionsMessage}
            className={`async-select ${disableSelect1 ? 'disabled' : ''}`} // Add a custom class for styling
          />
        </div>
        <div className="search-icon">
          <i className="fas fa-search"></i>
        </div>

      </div>
    )
  };



	const SearchJobtitleStyles = {
		control: (provided, state) => ({
			...provided,
			marginLeft: "16px",
			backgroundColor: "#fff",
			border: "1px solid #093D54",
			cursor: "text",
			color: "#000",
			":hover": {
				borderColor: "#000",
			},
		}),

		placeholder: (provided) => ({
			...provided,
			whiteSpace: "nowrap",        // Prevent line break
			overflow: "hidden",          // Hide overflow
			textOverflow: "ellipsis",    // Add "..." if too long
			fontSize: "13px",
		  }),

		singleValue: (provided) => ({
			...provided,
			borderRadius: "10px",
			backgroundColor: "#fff",
			color: "#000",
		}),

		multiValue: (provided) => ({
			...provided,
			borderRadius: "10px",
			backgroundColor: "white",
			marginTop: "8px",
			border: "1px solid",
			fontSize: "14px",
			color: "#000",
		}),

		multiValueRemove: (base) => ({
			...base,
			backgroundColor: "#fff",
			color: "#000",
			borderRadius: "10px",
			border: "1px solid #08CEDC",
			marginTop: "-7px",
			marginBottom: "2px",
			width: "11px",
			height: "11px",
			":hover": {
				backgroundColor: "#55C2C3",
				color: "black",
			},
		}),

		menu: (provided) => ({
			...provided,
			backgroundColor: "#fff",
			color: "#000",
			border: "1px solid #093D54",
			position: "absolute",       // Changed from relative
			width: "220px",
			left: "16px",                    // Aligns to the left of the input
			zIndex: 9999,
			fontSize:"14px"
		}),


		menuList: (provided) => ({
			...provided,
			backgroundColor: "#fff",
			color: "#000",
		}),

		option: (provided, state) => ({
			...provided,
			backgroundColor: state.isFocused ? "#f0f0f0" : "#fff",
			color: "#000",
			cursor: "pointer",
		}),

		valueContainer: (provided) => ({
			...provided,
			padding: "0px 5px 0 0",
			width: "300px",
			color: "#000",
		}),

		container: (provided) => ({
			...provided,
			padding: "0px 11px 0 0",
		}),

		group: (provided) => ({
			...provided,
			width: "262px",
			backgroundColor: "#fff",
			color: "#000",
			border: "1px solid #093D54",
		}),

		indicatorsContainer: (provided, state) => ({
			...provided,
			visibility: "hidden",
			fontSize: "0",
		}),

	};


  const IndustrySicCode = ({ autoFocus, onFocus, onBlur }) => {

    const loadOptions = (inputText, callback) => {
      if (inputText.length < 1) {
				callback([]);
				return;
			}
      if (inputText && inputText.length > 0) {
        const dataPost = {
          sic_code: inputText // Pass the inputText to your API request
        };
        // Make an API call here to fetch suggestions based on the inputText
        PostWithTokenNoCache(ApiName.esAutoSuggestions, dataPost)
          .then((res) => {
            if (res.data.status === 200) {
              const sic = JSON.parse(res.data.data);
              // Convert the array to a Set to remove duplicates and then convert it back to an array
              const uniqueSIC = [...new Set(sic)];

              const options = uniqueSIC.map((option) => ({
                label: option,
                value: option
              }));
              callback(options);
            }
          })
          .catch((error) => {
            console.error('Error fetching data:', error);
            callback([]); // Clear options on error
          });
      }
    };

    // Handle value change when an option is selected or deselected
    const handleValueChange = (selectedOptions) => {
      setDisableSelect1(true);
      setIsFiltered(true);

      setSelectedSICValues(selectedOptions);
      let updatedSICCode = {};

      // Update the company names based on selected options
      selectedOptions.forEach((item, index) => {
        updatedSICCode[index] = item.value;
      });
      setSicCode(updatedSICCode);
    };
    return (
      <div className="mb-2 mt-3 d-flex flex-column">
        <div className="">
          <AsyncSelect
            cacheOptions
            defaultOptions={false}
            loadOptions={loadOptions}
            isMulti
            placeholder="Search 4 Digit SIC code"
            onChange={handleValueChange} // Handle selected value changes
            value={selectedSICValues} // Pass selected values
            styles={SearchJobtitleStyles}
            autoFocus={autoFocus}
            onFocus={onFocus}
            onBlur={onBlur}
            isClearable
            isDisabled={disableSelect2} // Use the isDisabled prop for disabling
            noOptionsMessage={noOptionsMessage}
            className={`async-select ${disableSelect2 ? 'disabled' : ''}`} // Add a custom class for styling
          />
        </div>
        <div className="search-icon">
          <i className="fas fa-search"></i>
        </div>

      </div>
    )
  };

  const CompanyTypes = () => {

    const handleCompanyTypeValueChange = (event) => {
      setCurrentPage(1);
      setIsFiltered(true);

      const checkboxValue = event.target.value;
      if (checkboxValue) {
        if (checkboxValue === "Public" || checkboxValue === "Private") {
          if (companyTypeCheckedBoxes.includes(checkboxValue)) {
            setCompanyTypeCheckedBoxes(
              companyTypeCheckedBoxes.filter((checkbox) => checkbox !== checkboxValue)
            );
          } else {
            setCompanyTypeCheckedBoxes([...companyTypeCheckedBoxes, checkboxValue]);
          }
        }
      }
    }

    return (
      <div className="d-flex flex-column">
        {companyTypeOption && companyTypeOption.map((checkbox, i) => {
          const isDisabled = checkbox.label !== "Public" && checkbox.label !== "Private";
          
          return (
            <label key={i} className="job-checkbox">
              &nbsp;
              <input
                key={"in_" + checkbox.value}
                type="checkbox"
                value={checkbox.label}
                checked={companyTypeCheckedBoxes.includes(checkbox.label)}
                onChange={handleCompanyTypeValueChange}
                disabled={isDisabled}
                style={{
                  cursor: isDisabled ? 'not-allowed' : 'pointer',
                  opacity: isDisabled ? 0.5 : 1,
                  backgroundColor: isDisabled ? '#f1f1f1' : 'transparent',
                  borderColor: isDisabled ? '#ccc' : '#093D54',
                  marginRight: '5px', // Add any other styles you want for spacing or appearance
                }}
              />
              {checkbox.label}
            </label>
          );
        })}
      </div>
    );
  };

  const SearchCompanyKeyword = ({ autoFocus, onFocus, onBlur }) => {

    const loadOptions = (inputText, callback) => {
      if (inputText.length < 1) {
				callback([]);
				return;
			}
      if (inputText && inputText.length > 0) {
        const dataPost = {
          company_keywords_list: inputText // Pass the input to the API
        };
        PostWithTokenNoCache(ApiName.esAutoSuggestions, dataPost)
          .then(async (res) => {
            if (res.data.status === 200) {
              const company = JSON.parse(res.data.data);
              let properCaseCompany = await convertToProperCase(company);
              const uniqueCompany = [...new Set(properCaseCompany)];

              const options = uniqueCompany.map((option) => ({
                label: option,
                value: option
              }));
              callback(options);
            }
          })
          .catch((error) => {
            console.error('Error fetching data:', error);
            callback([]); // Clear options on error
          });
      } else {
        callback([]); // Prevent clearing input and returning empty options when input is too short
      }
    };

    // Handle value change when an option is selected or deselected
    const handleValueChange = (selectedOptions) => {
      setSelectedValues(selectedOptions);
      setIsFiltered(true);

      let updatedCompanyKeywords = {};

      // Update the company names based on selected options
      selectedOptions.forEach((item, index) => {
        updatedCompanyKeywords[index] = item.value;
      });
      setCompanyKeyword(updatedCompanyKeywords);
    };

    return (
      <div className="mb-2 mt-3 d-flex flex-column">
        <div className="">
          <AsyncSelect
            cacheOptions
            defaultOptions={false}
            loadOptions={loadOptions}
            isMulti
            placeholder="Search Company Keyword"
            onChange={handleValueChange}
            value={selectedValues}
            styles={SearchJobtitleStyles}
            autoFocus={autoFocus}
            onFocus={onFocus}
            onBlur={onBlur}
            isClearable
            noOptionsMessage={noOptionsMessage}
            className={`async-select`}
          />

        </div>
        <div className="search-icon">
          <i className="fas fa-search"></i>
        </div>

      </div>
    )
  }

  return (
    <div>
      <SearchIndustrys
        autoFocus={isIndustryAutoFocus}
        onFocus={() => setIsIndustryAutoFocus(true)}
        onBlur={() => setIsIndustryAutoFocus(false)}
      />
      <IndustrySicCode
        autoFocus={isSICCodeAutoFocus}
        onFocus={() => setIsSICCodeAutoFocus(true)}
        onBlur={() => setIsSICCodeAutoFocus(false)}
      />
      <SearchCompanyKeyword
        autoFocus={isCompanyKeywordAutoFocus}
        onFocus={() => setIsCompanyKeywordAutoFocus(true)}
        onBlur={() => setIsCompanyKeywordAutoFocus(false)}
      />

      <div key={"revenueSize"}>
        <div>
          <div className="paragraph ">
            <button
              className="Job-Titles-1"
              type="button"
              data-toggle="collapse"
              data-target="#collapseExample1Rev"
              aria-expanded="false"
              aria-controls="collapseExample"
            >
              Select Company Type<i className="fa fa-caret-down"></i>

            </button>
            <div className="collapse" id="collapseExample1Rev">
              <div className=" card-body-containerr">
                <CompanyTypes />
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>
  );
};

export default Industry;
