@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400&display=swap');


body {
    font-family: 'Poppins', sans-serif;
    font-weight: 400;
    /* Regular */
}

.unlock {
    background: #07C5D1;
    background: linear-gradient(271deg, rgba(7, 197, 209, 1) 0%, rgba(14, 152, 228, 1) 45%, rgba(17, 129, 238, 1) 65%, rgba(0, 76, 191, 1) 83%);
    padding: 20px;
    border-radius: 8px;
    margin: 20px 10px 0 10px;
}

p.worldwide {
    font-size: 20px;
    margin: 0;
    color: #fff;
    font-family: 'Poppins', sans-serif;

}

p.prospecting {
    font-size: 14px;
    color: #fff;
    margin: 0;
}

button.home-learn-more {
    border: 0;
    background-color: #323338;
    padding: 8px 20px 8px 20px;
    border-radius: 33px;
    color: #fff;
    margin: 8px 0 0 0;
    cursor: pointer;
    font-family: 'Poppins', sans-serif;
    outline: none;
}

.welcome-to-home-page-1 {
    background-color: #fff;
    padding: 20px 20px 20px 20px;
    margin: 2rem 0 0 10px;
    border-radius: 8px;
}

.welcome-to-home-page-2 {
    background-color: #fff;
    padding: 10px 10px 20px 20px;
    margin: 2rem 0 0 10px;
    border-radius: 8px;
}


button.yes-home {
    font-size: 10px;
    border: 1px solid #DDDDDD;
    margin: 0 6px 0 6px;
    width: 70px;
    padding: 4px 0 4px 0px;
    border-radius: 4px;
    font-family: 'Poppins', sans-serif;

}

button.yes-home:hover {
    background-color: #EBF1FF;
    border: 1px solid #0655FF;
    color: #0655FF;
    font-family: 'Poppins', sans-serif;

}

p.home-name {
    font-size: 24px;
    font-weight: 700;
    font-family: 'Poppins', sans-serif;

}

p.ur-plan {
    font-size: 12px;
    color: #999999;
    margin: 0;
    font-family: 'Poppins', sans-serif;

}

p.free-plan {
    font-size: 16px;
    font-weight: 700;
    margin: 0;
    font-family: 'Poppins', sans-serif;

}

span.renewal-plan {
    font-size: 10px;
    color: #a2a2a2;
    margin: 0;
    font-family: 'Poppins', sans-serif;
}

.accounts-credits-plan {
    background-color: #f5f8ff;
    border-radius: 8px;
    padding: 10px 10px 10px 10px;
}

p.account-credits-plan {
    margin: 10px 0 10px 0;
    font-weight: 700;
    font-family: 'Poppins', sans-serif;

}

p.explore-plans {
    margin: 1rem 0 3rem 0;
    text-align: center;
    font-size: 24px;
    color: #151417;
    font-family: 'Poppins', sans-serif;
    font-weight: 500;
}

button.start-your-search-plan {
    margin: 22px auto 4rem;
    display: block;
    background-color: #115EA3;
    border: 0;
    font-size: 14px;
    border-radius: 18px;
    padding: 6px 36px 6px 36px;
    color: #fff;
    cursor: pointer;
    font-family: 'Poppins', sans-serif;
}

p.install-plan {
    margin: 0 0px 6px 11px;
    font-size: 12px;
    font-family: 'Poppins', sans-serif;

}

p.install-plan-2 {
    font-size: 9px;
    padding: 0 0 0 12px;
    color: #bbbbbb;
    font-family: 'Poppins', sans-serif;

}

p.suggeted-plan {
    font-size: 14px;
    margin: 0 0 20px 0;
    font-family: 'Poppins', sans-serif;

}

p.feedback-plan {
    font-size: 12px;
    margin: 0 0 0 15px;
    font-weight: 600;
    font-family: 'Poppins', sans-serif;

}

.homev2-wrapper {
    padding: 0px 20px 20px 20px;
}