import { useContext, useEffect, useMemo, useRef, useState } from 'react';
import {
	FaRocket
} from 'react-icons/fa';
import { useNavigate } from 'react-router-dom';
import { AxiosPostBearer, fetchProfilePicture, PostWithTokenNoCache } from '../../customer/common-files/ApiCalls.js';
import { ApiName } from '../../customer/common-files/ApiNames.js';
import "../assests/css/filter/leftnavbarv2.css";
import '../assests/css/layouts/header.css';
import DashboardContext from "../common-files/ContextDashboard";
import { useMicrosoftLoginLogic } from '../common-files/SocialMediaAuth.js';
import UseTabStore from "../common-files/useGlobalState.js";
import Analytics from '../../utils/analyticsTracking.js';

const Header = () => {
	const upgradeBtnRef = useRef(null);
	const [isSecondDropDownOpen, setIsSecondDropDownOpen] = useState(false);
	const dropdownRef = useRef(null);
	const [isDropdownOpen, setIsDropdownOpen] = useState(false);
	const [userCredits, setUserCredits] = useState(null);

	// Format credit values consistently
	const formatCreditValue = (value) => {
		if (value === null || value === undefined) return "0";
		if (typeof value === "number") return value.toLocaleString("en-IN");
		if (typeof value === "string" && value.toLowerCase() === "unlimited")
			return "Unlimited";
		return value;
	};

	const { dataDC } = useContext(DashboardContext);
	const [user] = useState(JSON.parse(localStorage.getItem('user')));
	const [contactData, setContactData] = useState({
		firstName: null,
		lastName: null,
		email: null,
		token: null,
	});
	const [profilePicture, setProfilePicture] = useState(null);
	const { Microsoftlogout } = useMicrosoftLoginLogic();
	const {
		profileUpdated,
		setSettingsActiveTab,
		setProfileUpdated,
		setButtonType,
		setDefaultAlert,
		setDefaultErrorMsg } = UseTabStore();
	const navigate = useNavigate();
	const [lastCalculated, setLastCalculated] = useState(
		new Date().toLocaleString()
	);
	useMemo(() => {
		if (!user) {
			window.location.href = "/";
		}
	}, []);

	useEffect(() => {
		const urlParams = new URLSearchParams(window.location.search);
		if (urlParams.get("upgrade_popup") === "true" && upgradeBtnRef.current) {
			upgradeBtnRef.current.click();
		}
	}, []);

	useEffect(() => {
		// Function to handle the keydown event (Ctrl + A and Ctrl + C)
		const handleKeyDown = (event) => {
			// Check for Ctrl + A or Ctrl + C
			if ((event.ctrlKey && event.key === 'a') || (event.ctrlKey && event.key === 'c')) {
				event.preventDefault();
			}
		};

		// Function to handle the right-click event
		const handleRightClick = (event) => {
			event.preventDefault();
		};

		// Add the event listeners when the component mounts
		document.addEventListener('keydown', handleKeyDown);
		document.addEventListener('contextmenu', handleRightClick);

		// Cleanup event listeners when the component unmounts
		return () => {
			document.removeEventListener('keydown', handleKeyDown);
			document.removeEventListener('contextmenu', handleRightClick);
		};
	}, []);

	// Fetch user data
	useEffect(() => {
		const user = JSON.parse(localStorage.getItem("user"));
		const postData = () => {
			const requestBody = { "method": "GET" };
			AxiosPostBearer(ApiName.userData, requestBody, user.token)
				.then(function (response) {
					const jsonData = JSON.parse(response.data.data);
					const total = { ...jsonData, ...user };
					const merge = { ...contactData, ...total };
					setContactData(merge);
					localStorage.setItem('user', JSON.stringify(merge));
				}).catch(function () { });
		};
		postData();

		// Fetch user credits
		const fetchCredits = async () => {
			try {
				const res = await PostWithTokenNoCache(ApiName.activeCreadits, {});
				if (res?.data?.status === 200 && res.data.data) {
					setUserCredits(JSON.parse(res.data.data));
				}
			} catch (error) {
				console.error("Error fetching credits:", error);
			}
		};
		fetchCredits();
	}, []);

	// Get profile picture
	async function getUserProfilePicture(data) {
		const url = ApiName.getProfilePicture;
		const imageUrlTemp = await fetchProfilePicture(url, data);
		if (imageUrlTemp) {
			setProfilePicture(imageUrlTemp);
			setProfileUpdated("");
		} else {
			setProfilePicture("");
		}
	}

	useEffect(() => {
		getUserProfilePicture({});
	}, [profileUpdated])

	// Navigation functions
	const goToReachMax = () => {
		navigate('/refer-earn');
	}

	const account = () => {
		setSettingsActiveTab("profile");
		navigate('/settings', {
			state: { email: user.email, token: user.token }
		});
	}

	const openExtensionPage = (e) => {
		e.preventDefault(); // 🛑 Prevent default anchor/button behavior if used inside a link/form
		const extensionId = "nincdhaihcinfenbnmfacjkclcejomfi";
		const webStoreUrl = `https://chromewebstore.google.com/detail/${extensionId}`;

		window.open(webStoreUrl, "_blank"); // ✅ Only opens one new tab
	};

	const upgradePlan = () => {
		setButtonType("upgrade");
		setDefaultAlert(true);
		setDefaultErrorMsg("upgrade_plan");
	}

	// Sign out function
	const signOutSubmit = async () => {
		// Clear data and navigate
		sessionStorage.clear();
		UseTabStore.getState().clearStorage();

		if (localStorage.getItem('signin-type') === 'microsoft') {
			localStorage.removeItem('signin-type');
			Microsoftlogout();
		}

		PostWithTokenNoCache(ApiName.deviceLogout, {}).then((res) => {
			if (res?.status === 200) {
				localStorage.clear();
				navigate('/');
			}
		});
	}

	// Click outside handler for dropdowns
	useEffect(() => {
		const handleClickOutside = (event) => {
			if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
				setIsSecondDropDownOpen(false);
			}
		};
		document.addEventListener("mousedown", handleClickOutside);
		return () => document.removeEventListener("mousedown", handleClickOutside);
	}, []);

	// Get display name
	const getDisplayName = () => {
		if (user?.firstName && user.firstName !== null) {
			return `${user.firstName} ${user.lastName || ''}`.trim();
		}
		if (contactData?.firstName && contactData.firstName !== null) {
			return `${contactData.firstName} ${contactData.lastName || ''}`.trim();
		}
		return "User";
	};

	// Get truncated email
	const getTruncatedEmail = () => {
		const email = contactData.email || user?.email || "";
		return email.length > 16 ? email.slice(0, 16) + "..." : email;
	};

	const handleOpenExtension = (e) => {
		e.preventDefault(); // 🛑 Prevent default anchor/button behavior if used inside a link/form
		const extensionId = "nincdhaihcinfenbnmfacjkclcejomfi";
		const webStoreUrl = `https://chromewebstore.google.com/detail/${extensionId}`;

		window.open(webStoreUrl, "_blank"); // ✅ Only opens one new tab
	};

	// Fetch user credits
	const fetchUserCredits = async () => {
		try {
			const res = await PostWithTokenNoCache(ApiName.activeCreadits, {});

			if (res?.data?.status === 200 && res.data.data) {
				const creditData = JSON.parse(res.data.data);
				setUserCredits(creditData);
			}
		} catch (error) {
			console.error("Error fetching credits:", error);
			Analytics.track("Credits Fetch Error", {
				error_message: error?.response?.data?.message || "Unknown error",
			});
		}
	};

	return (
		<>
			<div className="main container-fluid">
				{/* Header */}
				<div className="main-header d-flex flex-row justify-content-end align-items-center mb-3">
					<div className="d-flex flex-row justify-content-end align-items-center">
						<div className="d-flex align-items-center">
							<button type="button" className="animated-extension-btn" onClick={handleOpenExtension}>
								<img src="images/chrome.png" alt="Chrome Icon" className="bump-icon" />
								Extension
							</button>


							<button className="animated-btn btn btn-sm" onClick={upgradePlan} ref={upgradeBtnRef}>
								Upgrade
							</button>

							<div className={`unique-avatar-dropdown-wrapper ${isSecondDropDownOpen ? "show" : ""}`}
								ref={dropdownRef}>
								<div onClick={() => setIsSecondDropDownOpen(!isSecondDropDownOpen)}
									style={{ cursor: 'pointer' }}>
									<img src="images/wallet.png" className="img-fluid" alt="avatar" width="18" />
								</div>

								{isSecondDropDownOpen && (
									<div className="unique-avatar-dropdown-menu">
										<p className="v2-account-credits">Account credits</p>
										<div className="d-flex flex-row">
											<div>
												<img
													src="images/accounts-credits-download.png"
													className="img-fluid"
													width="12"
													alt="download-icon"
												/>
											</div>
											<div>
												<p className="line">|</p>
											</div>
											<div>
												<p className="used-proggress">
													{userCredits
														? `${formatCreditValue(userCredits.total_assigned_credit - userCredits.total_balance_credit)} used of ${formatCreditValue(userCredits.total_assigned_credit)}`
														: "Loading..."}
												</p>
											</div>
										</div>
										<div className="progress" style={{ height: "5px" }}>
											<div
												className="progress-bar"
												role="progressbar"
												style={{
													width: userCredits
														? `${((userCredits.total_assigned_credit - userCredits.total_balance_credit) / userCredits.total_assigned_credit) * 100}%`
														: "0%",
												}}
											></div>
										</div>
										<div className="d-flex flex-row mt-3">
											<div>
												<img
													src="images/account-credits-view-icon.png"
													className="img-fluid"
													width="12"
													alt="view-icon"
												/>
											</div>
											<div>
												<p className="line">|</p>
											</div>
											<div>
												<p className="used-proggress">
													{userCredits
														? userCredits.user_plan_name === "freemium"
															? `${formatCreditValue(userCredits.total_assigned_email_credits - userCredits.total_balance_email_credits)} used of ${formatCreditValue(userCredits.total_assigned_email_credits)}`
															: "Unlimited views"
														: "Loading..."}
												</p>
											</div>
										</div>
										<div className="progress mb-2" style={{ height: "5px" }}>
											<div
												className="progress-bar"
												role="progressbar"
												style={{
													width: userCredits && userCredits.user_plan_name === "freemium"
														? `${((userCredits.total_assigned_email_credits - userCredits.total_balance_email_credits) / userCredits.total_assigned_email_credits) * 100}%`
														: "100%",
												}}
											></div>
										</div>
										<p className="calculated">
											Last calculated: {lastCalculated}{" "}
											<span onClick={fetchUserCredits}>
												<a href="">Refresh</a>
											</span>
										</p>
									</div>
								)}
							</div>
						</div>

						<div className="ml-3 mr-3">
							<div className="profile-dropdown-container position-relative">
								<div onClick={() => setIsDropdownOpen(!isDropdownOpen)}
									style={{ cursor: 'pointer' }}>
									<img
										src={profilePicture || "images/avetar.png"}
										className="img-fluid rounded-circle"
										alt="avatar"
										width="30"
										style={{ opacity: profilePicture ? null : "0.5" }}
									/>
								</div>

								{isDropdownOpen && (
									<div className="profile-menu shadow">
										<div className="d-flex flex-row">
											<img
												src={profilePicture || "images/upload-pp-v2.png"}
												className="upload-pp"
												alt=""
												style={{ opacity: profilePicture ? null : "0.5", width: "45px", margin: "5px 0 0 6px", height: "45px" }}
											/>
											<div className="profile-header d-flex flex-column">
												<p className="user-name">{getDisplayName()}</p>
												<small>{getTruncatedEmail()}</small>
											</div>
										</div>
										<hr className="dropdown-divider" />
										<ul className="list-unstyled mb-0">
											<li onClick={account}>
												<img src="images/new-profile.png" width="16" />
												My account
											</li>
											<li onClick={upgradePlan}>
												<img src="images/new-view-plans.png" width="16" />
												View Plans
											</li>
											<li>
												<img src="images/new-feedback.png" width="16" />
												Feedback
											</li>
											<li onClick={goToReachMax}>
												<img src="images/r&f.png" width="16" />
												Refer & Earn
											</li>
											<li className="highlight" onClick={openExtensionPage} >
												<FaRocket /> Install RS extension
											</li>
										</ul>
										<hr className="dropdown-divider" />
										<button type='button' onClick={signOutSubmit} className="sign-out">
											<img src="images/sign-out.png" /> Sign Out
										</button>
									</div>
								)}
							</div>
						</div>
					</div>
				</div>
			</div>
		</>
	);
}

export default Header;