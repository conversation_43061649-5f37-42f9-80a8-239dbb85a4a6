import { useState } from "react";
import React from "react";

const PlanInfoSidebar = ({ userCredits, userMembership, lastCalculated, fetchUserCredits, shake }) => {
  const formatCreditValue = (value) => {
    if (value === null || value === undefined) return "0";
    if (typeof value === "number") return value.toLocaleString("en-IN");
    if (typeof value === "string" && value.toLowerCase() === "unlimited") return "Unlimited";
    return value;
  };

  const [showModal, setShowModal] = useState(false);
  const [animateClose, setAnimateClose] = useState(false);

  const handleClose = () => {
    setAnimateClose(true); // start closing animation
    setTimeout(() => {
      setShowModal(false); // hide modal after animation
      setAnimateClose(false); // reset
    }, 300); // match CSS duration
  };

  return (
    <div className="my-free-box">
      <p className="my-ur-plan">Your Plan</p>
      <p className='my-free-plan'>{userMembership}</p>
      <p className="my-sept">
        <span className="renewal-plan">Renewal on</span> {lastCalculated}
      </p>

      <hr className="horizontal-plan" />

      <div className="accounts-credits-plan">
        <p className="my-account-credits-plan">Account credits</p>

        <div className="d-flex flex-row">
          <div>
            <img src="images/accounts-credits-download.png" className="img-fluid" width="12" alt="download" />
          </div>
          <div>
            <p className="line">|</p>
          </div>
          <div>
            <p className="used-proggress">
              {userCredits
                ? `${formatCreditValue(
                  userCredits.total_assigned_credit -
                  userCredits.total_balance_credit
                )} used of ${formatCreditValue(
                  userCredits.total_assigned_credit
                )}`
                : "Loading..."}
            </p>
          </div>
        </div>

        <div className="progress" style={{ height: "5px" }}>
          <div
            className="progress-bar"
            role="progressbar"
            style={{
              width: userCredits
                ? `${((userCredits.total_assigned_credit -
                  userCredits.total_balance_credit) /
                  userCredits.total_assigned_credit) *
                100
                }%`
                : "0%",
            }}
          />
        </div>

        <div className="d-flex flex-row mt-3">
          <div>
            <img src="images/account-credits-view-icon.png" className="img-fluid" width="12" alt="view" />
          </div>
          <div>
            <p className="line">|</p>
          </div>
          <div>
            <p className="used-proggress">
              {userCredits
                ? userCredits.user_plan_name === "freemium"
                  ? `${formatCreditValue(
                    userCredits.total_assigned_email_credits -
                    userCredits.total_balance_email_credits
                  )} used of ${formatCreditValue(
                    userCredits.total_assigned_email_credits
                  )}`
                  : "Unlimited views"
                : "Loading..."}
            </p>
          </div>
        </div>

        <div className="progress mb-2" style={{ height: "5px" }}>
          <div
            className="progress-bar"
            role="progressbar"
            style={{
              width:
                userCredits && userCredits.user_plan_name === "freemium"
                  ? `${((userCredits.total_assigned_email_credits -
                    userCredits.total_balance_email_credits) /
                    userCredits.total_assigned_email_credits) *
                  100
                  }%`
                  : "100%",
            }}
          />
        </div>

        <p className="calculated">
          Last calculated: {lastCalculated}{" "}
          <span onClick={fetchUserCredits}>
            <a href="#">Refresh</a>
          </span>
        </p>
      </div>

      <button
        className="want-more-credits"
        onClick={() => setShowModal(true)}
      >
        Want More Credits?
      </button>

      {showModal && (
        <div className={`credit-modal-overlay ${animateClose ? 'fade-out' : 'fade-in'}`}>
          <div className={`credit-modal-content ${animateClose ? 'slide-out' : 'slide-in'}`}>
            <button className="credit-modal-close" onClick={handleClose}>
              &times;
            </button>
            <p className="kindly-check">Kindly specify the number of export credits you need.</p>

            <div className="d-flex flex-row justify-content-center">
              <div>
                <input type="text" className="credit-input" placeholder="Enter your value" />
              </div>
              <div>
                <button type="button>" className="credit-button">Submit</button>
              </div>
            </div>
          </div>
        </div>
      )}

    </div>
  );
};

export default PlanInfoSidebar;