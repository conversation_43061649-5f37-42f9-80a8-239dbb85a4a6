import React, { useEffect, useMemo, useRef, useState, useContext } from "react";
import Header from "../../customer/layouts/Header.js";
import LeftNavbar from "../../customer/filters/LeftNavbar.js";
import penguineLoadingGif from "../../customer/assests/waiting.gif";
import "../assests/css/layouts/enrich.css";
import EnrichCustomDropdown from "./EnrichCustomDropdown.js";
import { useNavigate } from "react-router-dom";
import { PostWithTokenNoCache } from "../common-files/ApiCalls";
import { ApiName } from "../common-files/ApiNames";
import UseTabStore from '../common-files/useGlobalState.js';
import <PERSON> from 'papaparse';

const fieldOptions = ["First Name", "Last Name", "Job title", "Company", "ZipCode", "Phone", "Email Address", "Business Type"];

const companyFieldOptions = [
    "Company Name",
    "Website",
    "Company Size",
    "Address",
    "Phone Number",
    "SIC Code",
    "Industry",
    "Social Media URL"
];

const optionsData = [
    {
        id: 'emails',
        label: 'Emails',
        icon: 'images/open-envi.png',
    },
    {
        id: 'phones',
        label: 'Phones',
        icon: 'images/phone-envi.png',
    },
    {
        id: 'company',
        label: 'Company Attributes',
        icon: 'images/company-env.png',
    },
    {
        id: 'technology',
        label: 'Technology',
        icon: 'images/technology-env.png',
    },
];



const Enrich = ({ label, options }) => {
    const [isAuth, setIsAuth] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [activeTab, setActiveTab] = useState("CSV");
    const [showModal, setShowModal] = useState(false);
    const [isClosing, setIsClosing] = useState(false);
    const [enrichType, setEnrichType] = useState('contact'); // 'contact' or 'company'

    const [selectedFile, setSelectedFile] = useState(null);
    const fileInputRef = useRef(null);
    const [isDragging, setIsDragging] = useState(false);
    const [step, setStep] = useState(1);
    const [selected, setSelected] = useState(label);
    const [isOpen, setIsOpen] = useState(false);
    const [selectedOptions, setSelectedOptions] = useState(['emails']);
    const [showNewContainerAfterDone, setShowNewContainerAfterDone] = useState(false);
    const [showCrmTab, setShowCrmTab] = useState(false); // false hides CRM tab

    // CSV parsing states
    const [csvHeaders, setCsvHeaders] = useState([]);
    const [csvData, setCsvData] = useState([]);
    const [fieldMappings, setFieldMappings] = useState({});

    // New state variables for data loading
    const [enrichmentData, setEnrichmentData] = useState([]);
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(0);
    const pageSize = 10;

    // Get user token from localStorage
    const userData = JSON.parse(localStorage.getItem('user'));

    const {
        defaultAlert,
        defaultErrorMsg,
        setButtonType,
        setDefaultErrorMsg,
        setDefaultAlert
    } = UseTabStore();

    const navigate = useNavigate();

    const downloadSampleCSV = (type) => {
        const csvContent = type === 'company' ? [
            ['Company Name', 'Website', 'Company Size', 'Address', 'Phone Number', 'SIC Code', 'Industry', 'Social Media URL']
        ] : [
            ['First name', 'Last name', 'Job title', 'Company', 'Personal Linkedin URL', 'Email', 'Website']
        ];

        const blob = new Blob([csvContent.map(row => row.join(',')).join('\n')], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', `sample_${activeTab}_enrichment.csv`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    };

    const handleRedirect = () => {
        navigate("/data-enrich"); // <-- your route path here
    };

    // Data loading function similar to EnrichTabContacts.js
    const checkEnrichmentData = async (tab) => {
        setIsLoading(true);
        try {
            let params = {
                page: currentPage,
                pageSize: pageSize,
                searchParams: {
                    enrichmentType: tab.toUpperCase(),
                },
                sortBy: "desc"
            };

            const response = await PostWithTokenNoCache(ApiName.fetchAllEnrich, params);
            if (response?.status == 200 && response.data?.data) {
                let res = JSON.parse(response.data.data);
                if (res.enrichments?.records?.length > 0) {
                    setEnrichmentData(res.enrichments.records);
                    setTotalPages(res?.enrichments?.totalCount);
                    setIsLoading(false);
                } else {
                    setEnrichmentData([]);
                    setTotalPages(res?.enrichments?.totalCount);
                    setIsLoading(false);
                }
            }
        } catch (error) {
            console.error("Error checking enrichment data:", error);
            setIsLoading(false);
        }
    };

    // Effect hook for data loading
    useEffect(() => {
        checkEnrichmentData(activeTab);
    }, [currentPage]);

    // Handle tab change
    const handleTabClick = (tab) => {
        setActiveTab(tab);
        setIsLoading(true);
        setEnrichmentData([]);
        setCurrentPage(1);
        setTimeout(() => {
            checkEnrichmentData(tab);
        }, 1000);
    };

    // Original effect hook
    useEffect(() => {
        if (!showCrmTab && activeTab === "CRM") {
            setActiveTab("CSV");
        }
    }, [showCrmTab, activeTab]);


    const toggleOption = (id) => {
        setSelectedOptions((prevOptions) =>
            prevOptions.includes(id)
                ? prevOptions.filter((option) => option !== id)
                : [...prevOptions, id]
        );
    };

    const handleSelect = (option) => {
        setSelected(option);
        setIsOpen(false);
    };


    const handleFileChange = (e) => {
        const file = e.target.files[0];
        if (!file) return;

        if (file.name.endsWith('.csv')) {
            setSelectedFile(file);
            parseCSV(file);
        } else {
            alert("Please upload a valid CSV file");
        }
    };

    const parseCSV = (file) => {
        Papa.parse(file, {
            header: true,
            preview: 5,
            skipEmptyLines: true,
            complete: (results) => {
                if (results.data.length > 0) {
                    const headers = Object.keys(results.data[0]);
                    setCsvHeaders(headers);
                    setCsvData(results.data);

                    const defaultMappings = {};
                    headers.forEach(header => {
                        defaultMappings[header] = header;
                    });
                    setFieldMappings(defaultMappings);
                }
            },
            error: (error) => {
                console.error("Error parsing CSV:", error);
                alert("Error parsing CSV file. Please check the file format.");
            }
        });
    };

    const handleDrop = (e) => {
        e.preventDefault();
        setIsDragging(false);
        const file = e.dataTransfer.files[0];
        if (file?.name.endsWith(".csv")) {
            setSelectedFile(file);
            parseCSV(file);
        }
    };

    const handleDragOver = (e) => {
        e.preventDefault();
        setIsDragging(true);
    };

    const handleDragLeave = () => {
        setIsDragging(false);
    };


    const openFileDialog = () => {
        fileInputRef.current.click();
    };


    const handleClose = () => {
        setIsClosing(true);
        setTimeout(() => {
            setShowModal(false);
            setIsClosing(false);
            // Reset CSV parsing states when modal is closed
            setSelectedFile(null);
            setCsvHeaders([]);
            setCsvData([]);
            setFieldMappings({});
            setStep(1);
        }, 300); // match CSS transition duration
    };

    const reuploadCSV = () => {
        setSelectedFile(null);
        setCsvHeaders([]);
        setCsvData([]);
        setFieldMappings({});
        setStep(1);
        setTimeout(() => {
            if (fileInputRef.current) {
                fileInputRef.current.click();
            }
        }, 100);
    };

    const handleFieldMappingChange = (field, csvHeader) => {
        setFieldMappings(prev => ({
            ...prev,
            [field]: csvHeader
        }));
    };



    useMemo(() => {
        const user = JSON.parse(localStorage.getItem("user"));
        if (!user) {
            window.location.href = "/";
        } else {
            setIsAuth(true);
        }
    }, []);



    const submitEnrichment = async () => {
        try {
            setIsLoading(true);
            setShowModal(false); // close popup

            // Get the full CSV data for submission
            const fullData = await new Promise((resolve, reject) => {
                Papa.parse(selectedFile, {
                    header: true,
                    complete: (results) => {
                        if (results.data.length > 0) {
                            resolve(results.data);
                        } else {
                            reject(new Error("CSV file is empty"));
                        }
                    },
                    error: (error) => reject(error)
                });
            }).catch(error => {
                setButtonType("error");
                setDefaultErrorMsg(error.message || "Failed to parse CSV file");
                setDefaultAlert(true);
                throw error;
            });

            const hasValues = (obj) => {
                return Object.values(obj).some(
                    value => value !== null && value !== undefined && value !== ''
                );
            };

            // Process the data according to field mappings based on enrichment type
            let processedData;
            if (enrichType === 'contact') {
                processedData = fullData
                    .map(row => ({
                        first_name: row[fieldMappings["First Name"]] || '',
                        middle_name: row[fieldMappings["Middle Name"]] || '',
                        last_name: row[fieldMappings["Last Name"]] || '',
                        job_title: row[fieldMappings["Job title"]] || '',
                        company: row[fieldMappings["Company"]] || '',
                        linkedin_url: row[fieldMappings["Personal Linkedin URL"]] || '',
                        email: row[fieldMappings["Email"]] || '',
                        website: row[fieldMappings["Website"]] || ''
                    }))
                    .filter(record => hasValues(record));
            } else {
                // Company enrichment data structure
                processedData = fullData
                    .map(row => ({
                        company_company_name: row[fieldMappings["Company Name"]] || '',
                        company_website: row[fieldMappings["Website"]] || '',
                        company_size: row[fieldMappings["Company Size"]] || '',
                        company_street_address: row[fieldMappings["Address"]] || '',
                        company_phone_1: row[fieldMappings["Phone Number"]] || '',
                        company_sic_code: row[fieldMappings["SIC Code"]] || '',
                        company_industries: row[fieldMappings["Industry"]] || '',
                        company_social_media_url: row[fieldMappings["Social Media URL"]] || ''
                    }))
                    .filter(record => hasValues(record));
            }

            // Prepare the payload for API call
            const payload = {
                enrichmentType: enrichType === 'contact' ? 'CONTACT' : 'COMPANY',
                fileName: selectedFile?.name || (enrichType === 'contact' ? "contact_enrichment.csv" : "company_enrichment.csv"),
                requiredFields: enrichType === 'contact' ? [
                    "contact_first_name",
                    "contact_last_name",
                    "contact_job_title_1",
                    "contact_job_title_level_1",
                    "contact_email_1"
                ] : [
                    "company_company_name",
                    "company_website",
                    "company_phone_1"
                ],
                uploadData: processedData
            };

            // Make the API call to submit the enrichment job
            const response = await PostWithTokenNoCache(ApiName.dataEnrich, JSON.stringify(payload));

            // Show success message
            if (response?.status === 200) {
                // Show completion modal message
                setButtonType("success");
                setDefaultErrorMsg("Enrichment job submitted successfully");
                setDefaultAlert(true);
            }
            if (response?.status === 200) {
                setShowNewContainerAfterDone(true);
                navigate("/data-enrich");
            } else {
                throw new Error(response?.data?.message || "Failed to submit enrichment job");
            }
        } catch (error) {
            console.error("Error submitting enrichment:", error);
            setButtonType("error");
            setDefaultErrorMsg(error?.message || "Failed to submit enrichment job");
            setDefaultAlert(true);
        } finally {
            setIsLoading(false);
        }
    };


    const [isDropdownVisible, setDropdownVisible] = useState(false);
    const dropdownRef = useRef(null);

    const toggleDropdown = () => {
        setDropdownVisible(prev => !prev);
    };

    useEffect(() => {
        const handleClickOutside = (e) => {
            if (dropdownRef.current && !dropdownRef.current.contains(e.target)) {
                setDropdownVisible(false);
            }
        };
        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);

    return (
        <>
            {isAuth ? (
                <div>
                    <Header />

                    <div style={{ display: "none" }}>
                        <LeftNavbar />
                    </div>

                    {!isLoading ? (
                        <div className="main-body">
                            <div className="data-enrichment-up-page">
                                <p>Data Enrichment</p>
                            </div>

                            <div className="csv-tabs p-3">
                                {/* Tabs */}
                                <div className="d-flex flex-row border-bottom justify-content-between">
                                    {/* Tabs container */}
                                    <div className="d-flex flex-row">
                                        <div
                                            className={`enrich-tab-button py-2 ${activeTab === "CSV" ? "active-tab" : ""}`}
                                            onClick={() => setActiveTab("CSV")}
                                        >
                                            CSV
                                        </div>

                                        <div
                                            className={`enrich-tab-button py-2 ms-3 ${activeTab === "CRM" ? "active-tab" : ""} ${!showCrmTab ? "disabled-tab" : ""}`}
                                            onClick={() => {
                                                if (showCrmTab) setActiveTab("CRM");
                                            }}
                                            style={{ opacity: "0.3", cursor: "text" }}
                                        >
                                            CRM
                                        </div>
                                    </div>


                                    <div className="d-flex flex-row">
                                        <div className="image-dropdown-container">
                                            <div className="d-flex flex-row" onClick={toggleDropdown}>
                                                <div>
                                                    <p className="download-sample"><img src="images/down.png" /> Download sample &nbsp; <img src="images/enrich-arrow.png" /></p>
                                                </div>

                                            </div>
                                            <div className={`image-dropdown-menu ${isDropdownVisible ? 'open' : ''}`}>
                                                <p className="samples" onClick={() => {
                                                    setActiveTab('contact');
                                                    downloadSampleCSV('contact');
                                                    setDropdownVisible(false);
                                                }}>Sample for people</p>
                                                <hr style={{ padding: "0", margin: "0" }} />
                                                <p className="samples" onClick={() => {
                                                    setActiveTab('company');
                                                    downloadSampleCSV('company');
                                                    setDropdownVisible(false);
                                                }}>Sample for Companies</p>
                                            </div>
                                        </div>

                                        <div>

                                        </div>
                                        {/* <div>
                                            <p className="download-sample"><img src="images/down.png" /> Download sample &nbsp; <img src="images/enrich-arrow.png" /></p>
                                        </div> */}

                                        <div onClick={handleRedirect} style={{ cursor: "pointer" }}>
                                            <p className="download-sample">
                                                <img src="images/down.png" alt="down icon" /> &nbsp;
                                                Enriched List &nbsp;

                                            </p>
                                        </div>
                                        <div>

                                        </div>
                                    </div>
                                </div>


                                {/* Tab Content */}
                                <div className="tab-content-container h-100 p-3 mt-2 rounded">
                                    {activeTab === "CSV" &&
                                        <div className="container">
                                            <div className="row mx-auto justify-content-center">
                                                <div className="col-md-5">
                                                    <div className="white-boxx container my-4 rounded shadow-sm bg-white">
                                                        <div className="">
                                                            <img src="images/enrich-companies.png" className="d-block mx-auto" width="246" alt="" />
                                                        </div>

                                                        <div>
                                                            <p className="enrich-contacts">Enrich People</p>

                                                            <p className="information">Select a CSV of people and enrich their information.</p>


                                                        </div>

                                                        <button type="button" className="select-csv" onClick={() => {
                                                            setEnrichType('contact');
                                                            setShowModal(true);
                                                        }}>Select CSV File</button>



                                                    </div>
                                                </div>

                                                <div className="col-md-5">
                                                    <div className="white-boxx container my-4 rounded shadow-sm bg-white">
                                                        <div className="">
                                                            <img src="images/enrich-companies.png" className="d-block mx-auto" width="238" alt="" />
                                                        </div>

                                                        <div>
                                                            <p className="enrich-contacts">Enrich Company</p>

                                                            <p className="information">Select a CSV of companies and enrich their information.</p>


                                                        </div>
                                                        <button type="button" className="select-csv" onClick={() => {
                                                            setEnrichType('company');
                                                            setShowModal(true);
                                                        }}>Select CSV File</button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>}


                                    {showCrmTab && activeTab === "CRM" && (
                                        <div>
                                            <p>This is CRM content.</p>
                                        </div>
                                    )}


                                    <div>
                                        <p className="csv-companies-enrich">Enrich people or companies in a CSV</p>

                                        <p className="reachstream-database">Select a CSV file of contacts or companies to enrich using ReachStream's <br /> database.</p>
                                    </div>
                                </div>


                            </div>




                            {showModal && (
                                <div className={`custom-modal-overlay ${isClosing ? "fade-out" : "fade-in"}`}>
                                    <div className={`custom-modal ${isClosing ? "scale-out" : "scale-in"}`}>
                                        <div className="d-flex flex-row justify-content-between">
                                            <div className="enrich-border">
                                                <p className="enrich-popup-header">{enrichType === 'company' ? 'Enrich Company' : 'Enrich People'}</p>
                                            </div>
                                            <div>
                                                <span className="enrich-close-button" onClick={handleClose}>
                                                    <img src="images/enrichpopup.png" className="img-fluid" width="28" alt="enrichpopup-img" />
                                                </span>
                                            </div>
                                        </div>



                                        {step === 1 && (
                                            <div className="enrich-custom-modal-body">
                                                <div className="enrich-custom-modal-body-1">
                                                    <div className="choose-border">

                                                        <div>
                                                            <img src="images/csv-image.png" className="img-fluid" alt="CSV Icon" />
                                                        </div>

                                                        {!selectedFile ? (
                                                            <>
                                                                <p className="drag-and-drop">Drag & drop your CSV file</p>
                                                                <label onClick={openFileDialog} className="custom-file-trigger">
                                                                    <span className="the-or-span">or</span>  Upload CSV File
                                                                </label>
                                                            </>
                                                        ) : (
                                                            <>
                                                                <p className="file-name">{selectedFile.name}</p>
                                                                <span className="replace-file" onClick={openFileDialog}>
                                                                    Replace file
                                                                </span>
                                                            </>
                                                        )}

                                                        <input
                                                            type="file"
                                                            accept=".csv"
                                                            ref={fileInputRef}
                                                            className="hidden-file"
                                                            onChange={handleFileChange}
                                                        />
                                                    </div>

                                                    <div>
                                                        <p className="uolpade-your-csv-here">
                                                            {enrichType === 'company' ? 'Upload Your CSV company list' : 'Upload Your CSV people list'}
                                                        </p>
                                                        <p className="select-csv">
                                                            {enrichType === 'company'
                                                                ? 'Select a CSV of companies to enrich it with information from ReachStream.'
                                                                : 'Select a CSV of people to enrich it with information from ReachStream.'
                                                            }
                                                        </p>
                                                    </div>

                                                </div>


                                                <div className="modal-footer">
                                                    <button className="next-button" type="button" disabled={!selectedFile} onClick={() => setStep(2)}>
                                                        Next
                                                    </button>
                                                </div>
                                            </div>
                                        )}

                                        {step === 2 && (
                                            <div className="next-step-container">
                                                <div className="display-csv-file">
                                                    <div className="d-flex flex-row justify-content-between">
                                                        <div className="d-flex flex-row">
                                                            <div><img src="images/noun-csv.png" className="img-fluid" alt="" /></div>
                                                            <div><p className="referred-csv">{selectedFile?.name || "No file selected"}</p></div>
                                                        </div>
                                                        <div className="d-flex flex-row">
                                                            <div>
                                                                <img src="images/reupload-csv.png" className="img-fluid" alt="" />
                                                            </div>
                                                            <div>
                                                                <p className="reupload-csv" onClick={reuploadCSV} style={{ cursor: 'pointer' }}>Reupload CSV</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div className="dummy-space">
                                                    <div className="scrollable-table-wrapper">

                                                        <table className="custom-scroll-table">
                                                            <thead>
                                                                <tr>
                                                                    <th>ReachStream field</th>
                                                                    <th>CSV column header</th>
                                                                    <th>Preview</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                {enrichType === 'contact' ? (
                                                                    /* Contact Fields */
                                                                    <>
                                                                        <tr>
                                                                            <td>First name*</td>
                                                                            <td>
                                                                                <EnrichCustomDropdown
                                                                                    label={fieldMappings["First Name"] || "First Name"}
                                                                                    options={csvHeaders.length > 0 ? csvHeaders : fieldOptions}
                                                                                    onSelect={(selected) => handleFieldMappingChange("First Name", selected)}
                                                                                />
                                                                            </td>
                                                                            <td className="table-last-column">
                                                                                {csvData.length > 0 && csvHeaders.length > 0 && fieldMappings["First Name"] ?
                                                                                    csvData.slice(0, 3).map(row => row[fieldMappings["First Name"]]).filter(Boolean).join(', ') :
                                                                                    "No preview available"}
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td>Middle name</td>
                                                                            <td>
                                                                                <EnrichCustomDropdown
                                                                                    label={fieldMappings["Middle Name"] || "Middle Name"}
                                                                                    options={csvHeaders.length > 0 ? csvHeaders : fieldOptions}
                                                                                    onSelect={(selected) => handleFieldMappingChange("Middle Name", selected)}
                                                                                />
                                                                            </td>
                                                                            <td className="table-last-column">
                                                                                {csvData.length > 0 && csvHeaders.length > 0 && fieldMappings["Middle Name"] ?
                                                                                    csvData.slice(0, 3).map(row => row[fieldMappings["Middle Name"]]).filter(Boolean).join(', ') :
                                                                                    "No preview available"}
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td>Last name*</td>
                                                                            <td>
                                                                                <EnrichCustomDropdown
                                                                                    label={fieldMappings["Last Name"] || "Last Name"}
                                                                                    options={csvHeaders.length > 0 ? csvHeaders : fieldOptions}
                                                                                    onSelect={(selected) => handleFieldMappingChange("Last Name", selected)}
                                                                                />
                                                                            </td>
                                                                            <td className="table-last-column">
                                                                                {csvData.length > 0 && csvHeaders.length > 0 && fieldMappings["Last Name"] ?
                                                                                    csvData.slice(0, 3).map(row => row[fieldMappings["Last Name"]]).filter(Boolean).join(', ') :
                                                                                    "No preview available"}
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td>Job title*</td>
                                                                            <td>
                                                                                <EnrichCustomDropdown
                                                                                    label={fieldMappings["Job title"] || "Job Title"}
                                                                                    options={csvHeaders.length > 0 ? csvHeaders : fieldOptions}
                                                                                    onSelect={(selected) => handleFieldMappingChange("Job title", selected)}
                                                                                />
                                                                            </td>
                                                                            <td className="table-last-column">
                                                                                {csvData.length > 0 && csvHeaders.length > 0 && fieldMappings["Job title"] ?
                                                                                    csvData.slice(0, 3).map(row => row[fieldMappings["Job title"]]).filter(Boolean).join(', ') :
                                                                                    "No preview available"}
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td>Company*</td>
                                                                            <td>
                                                                                <EnrichCustomDropdown
                                                                                    label={fieldMappings["Company"] || "Company"}
                                                                                    options={csvHeaders.length > 0 ? csvHeaders : fieldOptions}
                                                                                    onSelect={(selected) => handleFieldMappingChange("Company", selected)}
                                                                                />
                                                                            </td>
                                                                            <td className="table-last-column">
                                                                                {csvData.length > 0 && csvHeaders.length > 0 && fieldMappings["Company"] ?
                                                                                    csvData.slice(0, 3).map(row => row[fieldMappings["Company"]]).filter(Boolean).join(', ') :
                                                                                    "No preview available"}
                                                                            </td>
                                                                        </tr>
                                                                        <tr className="dotted-row">
                                                                            <td colSpan="3">
                                                                                <div className="dotted-line"></div>
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td>Personal Linkedin URL</td>
                                                                            <td>
                                                                                <EnrichCustomDropdown
                                                                                    label={fieldMappings["Personal Linkedin URL"] || "select"}
                                                                                    options={csvHeaders.length > 0 ? csvHeaders : fieldOptions}
                                                                                    onSelect={(selected) => handleFieldMappingChange("Personal Linkedin URL", selected)}
                                                                                />
                                                                            </td>
                                                                            <td className="table-last-column">
                                                                                {csvData.length > 0 && csvHeaders.length > 0 && fieldMappings["Personal Linkedin URL"] ?
                                                                                    csvData.slice(0, 3).map(row => row[fieldMappings["Personal Linkedin URL"]]).filter(Boolean).join(', ') :
                                                                                    "No preview available"}
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td>Email</td>
                                                                            <td>
                                                                                <EnrichCustomDropdown
                                                                                    label={fieldMappings["Email"] || "Email"}
                                                                                    options={csvHeaders.length > 0 ? csvHeaders : fieldOptions}
                                                                                    onSelect={(selected) => handleFieldMappingChange("Email", selected)}
                                                                                />
                                                                            </td>
                                                                            <td className="table-last-column">
                                                                                {csvData.length > 0 && csvHeaders.length > 0 && fieldMappings["Email"] ?
                                                                                    csvData.slice(0, 3).map(row => row[fieldMappings["Email"]]).filter(Boolean).join(', ') :
                                                                                    "No preview available"}
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td>Website</td>
                                                                            <td>
                                                                                <EnrichCustomDropdown
                                                                                    label={fieldMappings["Website"] || "Website"}
                                                                                    options={csvHeaders.length > 0 ? csvHeaders : fieldOptions}
                                                                                    onSelect={(selected) => handleFieldMappingChange("Website", selected)}
                                                                                />
                                                                            </td>
                                                                            <td className="table-last-column">
                                                                                {csvData.length > 0 && csvHeaders.length > 0 && fieldMappings["Website"] ?
                                                                                    csvData.slice(0, 3).map(row => row[fieldMappings["Website"]]).filter(Boolean).join(', ') :
                                                                                    "No preview available"}
                                                                            </td>
                                                                        </tr>
                                                                    </>
                                                                ) : (
                                                                    /* Company Fields */
                                                                    <>
                                                                        <tr>
                                                                            <td>Company Name*</td>
                                                                            <td>
                                                                                <EnrichCustomDropdown
                                                                                    label={fieldMappings["Company Name"] || "Company Name"}
                                                                                    options={csvHeaders.length > 0 ? csvHeaders : companyFieldOptions}
                                                                                    onSelect={(selected) => handleFieldMappingChange("Company Name", selected)}
                                                                                />
                                                                            </td>
                                                                            <td className="table-last-column">
                                                                                {csvData.length > 0 && csvHeaders.length > 0 && fieldMappings["Company Name"] ?
                                                                                    csvData.slice(0, 3).map(row => row[fieldMappings["Company Name"]]).filter(Boolean).join(', ') :
                                                                                    "No preview available"}
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td>Website*</td>
                                                                            <td>
                                                                                <EnrichCustomDropdown
                                                                                    label={fieldMappings["Website"] || "Website"}
                                                                                    options={csvHeaders.length > 0 ? csvHeaders : companyFieldOptions}
                                                                                    onSelect={(selected) => handleFieldMappingChange("Website", selected)}
                                                                                />
                                                                            </td>
                                                                            <td className="table-last-column">
                                                                                {csvData.length > 0 && csvHeaders.length > 0 && fieldMappings["Website"] ?
                                                                                    csvData.slice(0, 3).map(row => row[fieldMappings["Website"]]).filter(Boolean).join(', ') :
                                                                                    "No preview available"}
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td>Company Size</td>
                                                                            <td>
                                                                                <EnrichCustomDropdown
                                                                                    label={fieldMappings["Company Size"] || "Company Size"}
                                                                                    options={csvHeaders.length > 0 ? csvHeaders : companyFieldOptions}
                                                                                    onSelect={(selected) => handleFieldMappingChange("Company Size", selected)}
                                                                                />
                                                                            </td>
                                                                            <td className="table-last-column">
                                                                                {csvData.length > 0 && csvHeaders.length > 0 && fieldMappings["Company Size"] ?
                                                                                    csvData.slice(0, 3).map(row => row[fieldMappings["Company Size"]]).filter(Boolean).join(', ') :
                                                                                    "No preview available"}
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td>Address</td>
                                                                            <td>
                                                                                <EnrichCustomDropdown
                                                                                    label={fieldMappings["Address"] || "Address"}
                                                                                    options={csvHeaders.length > 0 ? csvHeaders : companyFieldOptions}
                                                                                    onSelect={(selected) => handleFieldMappingChange("Address", selected)}
                                                                                />
                                                                            </td>
                                                                            <td className="table-last-column">
                                                                                {csvData.length > 0 && csvHeaders.length > 0 && fieldMappings["Address"] ?
                                                                                    csvData.slice(0, 3).map(row => row[fieldMappings["Address"]]).filter(Boolean).join(', ') :
                                                                                    "No preview available"}
                                                                            </td>
                                                                        </tr>
                                                                        <tr className="dotted-row">
                                                                            <td colSpan="3">
                                                                                <div className="dotted-line"></div>
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td>Phone Number</td>
                                                                            <td>
                                                                                <EnrichCustomDropdown
                                                                                    label={fieldMappings["Phone Number"] || "Phone Number"}
                                                                                    options={csvHeaders.length > 0 ? csvHeaders : companyFieldOptions}
                                                                                    onSelect={(selected) => handleFieldMappingChange("Phone Number", selected)}
                                                                                />
                                                                            </td>
                                                                            <td className="table-last-column">
                                                                                {csvData.length > 0 && csvHeaders.length > 0 && fieldMappings["Phone Number"] ?
                                                                                    csvData.slice(0, 3).map(row => row[fieldMappings["Phone Number"]]).filter(Boolean).join(', ') :
                                                                                    "No preview available"}
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td>SIC Code</td>
                                                                            <td>
                                                                                <EnrichCustomDropdown
                                                                                    label={fieldMappings["SIC Code"] || "SIC Code"}
                                                                                    options={csvHeaders.length > 0 ? csvHeaders : companyFieldOptions}
                                                                                    onSelect={(selected) => handleFieldMappingChange("SIC Code", selected)}
                                                                                />
                                                                            </td>
                                                                            <td className="table-last-column">
                                                                                {csvData.length > 0 && csvHeaders.length > 0 && fieldMappings["SIC Code"] ?
                                                                                    csvData.slice(0, 3).map(row => row[fieldMappings["SIC Code"]]).filter(Boolean).join(', ') :
                                                                                    "No preview available"}
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td>Industry</td>
                                                                            <td>
                                                                                <EnrichCustomDropdown
                                                                                    label={fieldMappings["Industry"] || "Industry"}
                                                                                    options={csvHeaders.length > 0 ? csvHeaders : companyFieldOptions}
                                                                                    onSelect={(selected) => handleFieldMappingChange("Industry", selected)}
                                                                                />
                                                                            </td>
                                                                            <td className="table-last-column">
                                                                                {csvData.length > 0 && csvHeaders.length > 0 && fieldMappings["Industry"] ?
                                                                                    csvData.slice(0, 3).map(row => row[fieldMappings["Industry"]]).filter(Boolean).join(', ') :
                                                                                    "No preview available"}
                                                                            </td>
                                                                        </tr>
                                                                        <tr>
                                                                            <td>Social Media URL</td>
                                                                            <td>
                                                                                <EnrichCustomDropdown
                                                                                    label={fieldMappings["Social Media URL"] || "Social Media URL"}
                                                                                    options={csvHeaders.length > 0 ? csvHeaders : companyFieldOptions}
                                                                                    onSelect={(selected) => handleFieldMappingChange("Social Media URL", selected)}
                                                                                />
                                                                            </td>
                                                                            <td className="table-last-column">
                                                                                {csvData.length > 0 && csvHeaders.length > 0 && fieldMappings["Social Media URL"] ?
                                                                                    csvData.slice(0, 3).map(row => row[fieldMappings["Social Media URL"]]).filter(Boolean).join(', ') :
                                                                                    "No preview available"}
                                                                            </td>
                                                                        </tr>
                                                                    </>
                                                                )}


                                                                {/* Add more rows as needed */}
                                                            </tbody>
                                                        </table>
                                                    </div>

                                                </div>


                                                <div className="d-flex flex-row justify-content-between mt-2 mb-2">
                                                    <div>
                                                        <button className="csv-back-button" onClick={() => setStep(1)}>
                                                            &lt; Back
                                                        </button>
                                                    </div>
                                                    <div>
                                                        <button className="next-button" type="button" disabled={!selectedFile} onClick={() => setStep(3)}>Next</button>

                                                    </div>

                                                </div>
                                            </div>
                                        )}


                                        {step === 3 && (
                                            <>
                                                <div className="enrich-custom-modal-body-1">
                                                    <div className="d-flex gap-3 flex-wrap">
                                                        {optionsData.map((option) => (
                                                            <div
                                                                key={option.id}
                                                                className={`option-card ${selectedOptions.includes(option.id) ? 'active' : ''
                                                                    }`}
                                                                onClick={() => toggleOption(option.id)}
                                                            >
                                                                <input
                                                                    className="form-check-input"
                                                                    type="checkbox"
                                                                    checked={selectedOptions.includes(option.id)}
                                                                    readOnly
                                                                />
                                                                <img src={option.icon} alt={option.label} />
                                                                <div className="label-text">{option.label}</div>
                                                            </div>
                                                        ))}
                                                    </div>

                                                    <div>
                                                        <p className="what-whould-you-like">What would you like to enrich?</p>

                                                        <p className="charged">You will only be for contacts matched in your file.</p>
                                                    </div>
                                                </div>
                                                <div className="d-flex flex-row justify-content-between mt-2 mb-2">
                                                    <div>
                                                        <button className="csv-back-button" onClick={() => setStep(1)}>
                                                            &lt; Back
                                                        </button>
                                                    </div>
                                                    <div>
                                                        <button className="next-button" type="button" disabled={!selectedFile} onClick={() => setStep(4)}>Next</button>
                                                    </div>

                                                </div>
                                            </>
                                        )}

                                        {step === 4 && (
                                            <>
                                                <div className="enrich-custom-modal-body-1">

                                                    <div className="mt-4 mb-4">
                                                        <img src="images/final-csv.png" className="img-fluid mx-auto d-block" width="300" />
                                                    </div>

                                                    <p className="enriching-contacts">Your enrichment list is in progress</p>

                                                    <p className="notify-you">Your enriched list is being prepared. We'll notify you at <br /> <span className="make-it-bold"><EMAIL></span> when it's completed.</p>

                                                    <button className="upload-another-csv">Upload another CSV</button>

                                                </div>

                                                <div className="d-flex flex-row justify-content-end mt-2 mb-2">
                                                    <button
                                                        type="button"
                                                        className="next-button"
                                                        onClick={() => {
                                                            submitEnrichment();
                                                            // show final container
                                                        }}
                                                    >
                                                        Done
                                                    </button>

                                                </div>

                                            </>

                                        )}





                                    </div>


                                </div>
                            )}

                            {/* {showNextPage && (
                                <div className="mt-5 p-4 border rounded text-center bg-light">
                                    <h4>You're on the next page!</h4>
                                    <p>This replaces the tab content after clicking Done.</p>
                                </div>
                            )} */}
                        </div>
                    ) : (
                        <div className="d-flex flex-row justify-content-center mt-5">
                            <img
                                src={penguineLoadingGif}
                                alt="Loading"
                                className="loader"
                                width="400"
                            />
                        </div>
                    )}
                </div>
            ) : (
                <p>User is not authenticated.</p> // or redirect or login prompt
            )}
        </>
    );
};

export default Enrich;
