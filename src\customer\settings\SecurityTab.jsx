import React, { useState, useEffect } from "react";
import { PostWithTokenNoCache } from "../common-files/ApiCalls";
import { ApiName } from "../common-files/ApiNames";
import UseTabStore from "../common-files/useGlobalState";
import Alert from "../common-files/alert";

const SecurityTab = () => {
    const [currentPassword, setCurrentPassword] = useState("");
    const [newPassword, setNewPassword] = useState("");
    const [confirmPassword, setConfirmPassword] = useState("");
    const [logoutAllDevices, setLogoutAllDevices] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [accessHistory, setAccessHistory] = useState([]);
    const [loadingHistory, setLoadingHistory] = useState(true);

    // Real-time validation states
    const [currentPasswordError, setCurrentPasswordError] = useState("");
    const [newPasswordError, setNewPasswordError] = useState("");
    const [confirmPasswordError, setConfirmPasswordError] = useState("");
    const [passwordSuccessMessage, setPasswordSuccessMessage] = useState("");
    const [ipLocationCache, setIpLocationCache] = useState({});
    const [loadingLocations, setLoadingLocations] = useState({});

    const {
        setButtonType,
        setDefaultErrorMsg,
        setDefaultAlert,
        defaultAlert,
        defaultErrorMsg
    } = UseTabStore();

    useEffect(() => {
        const fetchAccessHistory = async () => {
            try {
                setLoadingHistory(true);
                const params = {
                    page: 1,
                    pageSize: 10,
                    sortBy: "createdAt",
                    searchParams: {
                        latest_by_day: 3
                    }
                };
                const response = await PostWithTokenNoCache(ApiName.accessHistory, params);
                if (response && response.data) {
                    const itemsWithLocations = await Promise.all(
                        response.data.items.map(async (item) => {
                            const ip = extractIP(item.device_info);
                            const location = ip ? await fetchLocationForIP(ip) : {
                                city: 'Unknown location',
                                country: ''
                            };
                            return {
                                ...item,
                                formattedDate: formatAccessDate(item.login_time || item.createdAt),
                                ipAddress: ip,
                                location: location
                            };
                        })
                    );
                    setAccessHistory(itemsWithLocations);
                }
            } catch (error) {
                setDefaultAlert(true);
                setDefaultErrorMsg('Failed to load access history. Please try again later.');
            } finally {
                setLoadingHistory(false);
            }
        };

        fetchAccessHistory();
    }, []);

    const extractIP = (deviceInfo) => {
        if (!deviceInfo) return null;
        const parts = deviceInfo.split(',').map(part => part.trim());

        // First pass - look for public IPs
        for (let i = parts.length - 1; i >= 0; i--) {
            const ipMatch = parts[i].match(/\b(?:\d{1,3}\.){3}\d{1,3}\b/);
            if (ipMatch && !isPrivateIP(ipMatch[0])) {
                return ipMatch[0];
            }
        }

        // Second pass - accept any IP if no public one found
        for (let i = parts.length - 1; i >= 0; i--) {
            const ipMatch = parts[i].match(/\b(?:\d{1,3}\.){3}\d{1,3}\b/);
            if (ipMatch) return ipMatch[0];
        }

        return null;
    };

    const isPrivateIP = (ip) => {
        return ip.startsWith('10.') ||
            ip.startsWith('192.168.') ||
            (ip.startsWith('172.') &&
                parseInt(ip.split('.')[1]) >= 16 &&
                parseInt(ip.split('.')[1]) <= 31);
    };

    const getDisplayLocation = (ip, location) => {
        if (!ip) return '';

        if (isPrivateIP(ip)) {
            return 'Corporate Network';
        }

        if (location.city === 'Unknown location') {
            return 'Unknown location';
        }

        // Show "City, Country" format when both available
        return location.country
            ? `${location.city}, ${location.country}`
            : location.city;
    };
    // Fetch location for IP using ipapi.co API
    const fetchLocationForIP = async (ip) => {
        if (!ip) return { city: 'Unknown location', country: '' };

        // Check cache first
        if (ipLocationCache[ip]) {
            return ipLocationCache[ip];
        }

        setLoadingLocations(prev => ({ ...prev, [ip]: true }));

        try {
            const response = await fetch(`https://ipapi.co/${ip}/json/`);
            if (!response.ok) throw new Error('Failed to fetch location');

            const data = await response.json();
            const location = {
                city: data.city || 'Unknown location',
                country: data.country_name || ''
            };

            // Update cache
            setIpLocationCache(prev => ({ ...prev, [ip]: location }));
            return location;
        } catch (error) {
            console.error('Error fetching location for IP:', ip, error);
            return { city: 'Unknown location', country: '' };
        } finally {
            setLoadingLocations(prev => ({ ...prev, [ip]: false }));
        }
    };

    const formatAccessDate = (dateString) => {
        if (!dateString) return '';

        try {
            const date = new Date(dateString);
            if (isNaN(date.getTime())) return '';

            return date.toLocaleDateString('en-US', {
                month: 'long',
                day: 'numeric',
                year: 'numeric'
            });
        } catch (e) {
            return '';
        }
    };

    // Parse device and browser info
    const parseDeviceBrowser = (deviceInfo) => {
        if (!deviceInfo) return 'Unknown device';

        if (deviceInfo.includes('Windows') && deviceInfo.includes('Chrome')) {
            return 'Windows Chrome';
        }
        if (deviceInfo.includes('Mac') && deviceInfo.includes('Safari')) {
            return 'Mac Safari';
        }
        if (deviceInfo.includes('Android') && deviceInfo.includes('Chrome')) {
            return 'Android Chrome';
        }

        return 'Unknown device';
    };
    const getUserEmail = () => {
        try {
            const user = JSON.parse(localStorage.getItem('user'));
            if (user) {
                return user.email || '';
            }
        } catch (error) {
            // Error handled silently
        }
        return '';
    };



    // Real-time validation functions
    const validateCurrentPassword = (password) => {
        if (!password.trim()) {
            setCurrentPasswordError("Current password is required");
            return false;
        }
        setCurrentPasswordError("");
        return true;
    };

    const validateNewPassword = (password, skipSamePasswordCheck = false) => {
        if (!password.trim()) {
            setNewPasswordError("New password is required");
            return false;
        }

        const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%&*()+=^.-])[A-Za-z\d!@#$%&*()+=^.-]{8,}$/;
        if (!passwordRegex.test(password)) {
            setNewPasswordError("Password must be at least 8 characters long, should contain at least one number, one upper case & lower case letter, one special character which includes !@#$%&*()-+=^.");
            return false;
        }

        // Real-time validation: Check if new password is same as current password
        // Skip this check if explicitly requested (for immediate error clearing)
        if (!skipSamePasswordCheck && currentPassword && password === currentPassword && password.trim() !== "") {
            setNewPasswordError("New password must be different from your current password");
            return false;
        }

        setNewPasswordError("");
        return true;
    };

    const validateConfirmPassword = (confirmPwd, newPwd) => {
        if (!confirmPwd.trim()) {
            setConfirmPasswordError("Confirm password is required");
            return false;
        }

        if (confirmPwd !== newPwd) {
            setConfirmPasswordError("New password and confirm password do not match");
            return false;
        }

        setConfirmPasswordError("");
        return true;
    };

    // Handle input changes with real-time validation
    const handleCurrentPasswordChange = (e) => {
        const value = e.target.value;
        setCurrentPassword(value);
        validateCurrentPassword(value);
        // Clear success message when user starts typing
        if (passwordSuccessMessage) {
            setPasswordSuccessMessage("");
        }
        // Re-validate new password to check for same password scenario
        if (newPassword) {
            validateNewPassword(newPassword);
        }
    };

    const handleNewPasswordChange = (e) => {
        const value = e.target.value;
        setNewPassword(value);

        // Clear success message when user starts typing
        if (passwordSuccessMessage) {
            setPasswordSuccessMessage("");
        }

        // Clear the "same password" error immediately when user starts typing
        if (newPasswordError === "New password must be different from your current password") {
            setNewPasswordError("");
            // Validate without the same password check initially to allow immediate clearing
            setTimeout(() => {
                validateNewPassword(value);
            }, 100);
        } else {
            validateNewPassword(value);
        }

        // Re-validate confirm password if it exists
        if (confirmPassword) {
            validateConfirmPassword(confirmPassword, value);
        }
    };

    const handleConfirmPasswordChange = (e) => {
        const value = e.target.value;
        setConfirmPassword(value);
        // Clear success message when user starts typing
        if (passwordSuccessMessage) {
            setPasswordSuccessMessage("");
        }
        validateConfirmPassword(value, newPassword);
    };

    // Form validation for submission
    const validateForm = () => {
        const isCurrentPasswordValid = validateCurrentPassword(currentPassword);
        const isNewPasswordValid = validateNewPassword(newPassword);
        const isConfirmPasswordValid = validateConfirmPassword(confirmPassword, newPassword);

        return isCurrentPasswordValid && isNewPasswordValid && isConfirmPasswordValid;
    };

    // Handle password change
    const handlePasswordChange = async () => {
        if (!validateForm()) {
            return;
        }

        setIsLoading(true);
        const email = getUserEmail();

        if (!email) {
            setButtonType("error");
            setDefaultErrorMsg("Enter a valid email address");
            setDefaultAlert(true);
            setIsLoading(false);
            return;
        }

        const params = {
            email: email,
            currentPassword: currentPassword,
            newPassword: newPassword,
            confirmPassword: confirmPassword,
            logOutAllDevices: logoutAllDevices
        };

        try {
            const response = await PostWithTokenNoCache(ApiName.changePassword, params);

            if (response && response.data) {
                if (response.data.status === 200) {
                    // Handle success response with inline message
                    setPasswordSuccessMessage(response?.data?.message || "Password changed successfully!");

                    // Clear form and error messages
                    setCurrentPassword("");
                    setNewPassword("");
                    setConfirmPassword("");
                    setLogoutAllDevices(false); // Reset to unchecked state
                    setCurrentPasswordError("");
                    setNewPasswordError("");
                    setConfirmPasswordError("");

                    // Ensure modal alert is not triggered
                    setDefaultAlert(false);
                    setDefaultErrorMsg("");
                    setButtonType("");

                    // Clear success message after 5 seconds
                    setTimeout(() => {
                        setPasswordSuccessMessage("");
                    }, 5000);
                }
            }

        } catch (error) {
            // Handle network errors and other exceptions
            const status = error.response.status;
            const message = error?.response?.data?.message || "An error occurred";

            if (status === 400) {
                // Handle client errors
                const errorMessage = message || "Enter a valid password";
                setCurrentPasswordError(errorMessage);
                setButtonType("error");
                setDefaultErrorMsg(message || "Network error occurred. Please try again.");
                setDefaultAlert(true);
            } else {
                setButtonType("error");
                setDefaultErrorMsg(message || "Network error occurred. Please try again.");
                setDefaultAlert(true);
            }
        } finally {
            setIsLoading(false);
        }
    };
    return (
        <>
            <div className="profile-box-2">
                <div className="row">
                    <div className="col-md-2"></div>
                    <div className="col-md-8">
                        <div className="profile-box">

                            <div className="d-flex flex-row justify-content-between">
                                <div>
                                    <p className="change-password">Change Passsword</p>
                                </div>
                            </div>
                            <div className="my-security" style={{ display: 'block !important', visibility: 'visible !important' }}>
                                <div className="row">
                                    <div className="col-md-4">
                                        <div className="input-wrapper-2">
                                            <label htmlFor="currentPassword">Your Current Password</label>
                                        </div>
                                    </div>
                                    <div className="col-md-6">
                                        <div className="input-wrapper-2">
                                            <input
                                                type="text"
                                                id="currentPassword"
                                                name="currentPassword"
                                                className="input-bottom-border-2"
                                                placeholder="Enter your current password"
                                                value={currentPassword}
                                                onChange={handleCurrentPasswordChange}
                                                autoComplete="off"
                                            />
                                            {currentPasswordError && (
                                                <div className="hanlde-error-message">
                                                    {currentPasswordError}
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </div>

                                <div className="row">
                                    <div className="col-md-4">
                                        <div className="input-wrapper-2">
                                            <label htmlFor="newPassword">New Password</label>
                                        </div>
                                    </div>
                                    <div className="col-md-6">
                                        <div className="input-wrapper-2">
                                            <input
                                                type="text"
                                                id="newPassword"
                                                name="newPassword"
                                                className="input-bottom-border-2"
                                                placeholder="Enter your new password"
                                                value={newPassword}
                                                onChange={handleNewPasswordChange}
                                                autoComplete="off"
                                            />
                                            {newPasswordError && (
                                                <div className="hanlde-error-message">
                                                    {newPasswordError}
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </div>

                                <div className="row">
                                    <div className="col-md-4">
                                        <div className="input-wrapper-2">
                                            <label htmlFor="confirmPassword">Confirm New Password</label>
                                        </div>
                                    </div>
                                    <div className="col-md-6">
                                        <div className="input-wrapper-2">
                                            <input
                                                type="text"
                                                id="confirmPassword"
                                                name="confirmPassword"
                                                className="input-bottom-border-2"
                                                placeholder="Enter your confirmed new password"
                                                value={confirmPassword}
                                                onChange={handleConfirmPasswordChange}
                                                autoComplete="off"
                                            />
                                            {confirmPasswordError && (
                                                <div className="hanlde-error-message">
                                                    {confirmPasswordError}
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </div>

                                <div className="my-last-step-2">
                                    <div>
                                        <label className="custom-checkbox">
                                            <input
                                                type="checkbox"
                                                name="logoutAllDevices"
                                                checked={logoutAllDevices}
                                                onChange={(e) => setLogoutAllDevices(e.target.checked)}
                                            />
                                            <span className="i-agree">Log me out of all device</span>
                                        </label>
                                    </div>
                                    <button
                                        type="button"
                                        className="save-out"
                                        disabled={isLoading}
                                        onClick={handlePasswordChange}
                                    >
                                        {isLoading ? "Saving..." : "Save"}
                                    </button>

                                    {passwordSuccessMessage && (
                                        <div >
                                            <p className="password-changed-successfully">{passwordSuccessMessage}</p>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="col-md-2"></div>
                </div>


                <div className="row">
                    <div className="col-md-2"></div>
                    <div className="col-md-8">
                        <div className="profile-box mt-3">
                            <div className="d-flex flex-row justify-content-between">
                                <div>
                                    <p className="change-password">Recent security activity</p>
                                    <p className="security-activity">Security activity and alerts from the last 3 days.</p>
                                </div>
                            </div>

                            {loadingHistory ? (
                                <div className="text-center py-4">Loading activity...</div>
                            ) : accessHistory.length > 0 ? (
                                <div className="my-security">
                                    {accessHistory.map((activity, index) => {
                                        const displayLocation = getDisplayLocation(activity.ipAddress, activity.location);
                                        return (
                                            <div className="row" key={activity.id || index}>
                                                <div className="col-md-4">
                                                    <p className="my-date">{activity.formattedDate}</p>
                                                </div>
                                                <div className="col-md-8">
                                                    <p className="current-session">
                                                        {parseDeviceBrowser(activity.device_info)}
                                                        {activity.status === 'active' && (
                                                            <span> (Current session) • {displayLocation} ({activity.ipAddress})</span>
                                                        )}
                                                        {activity.status !== 'active' && activity.ipAddress && (
                                                            <span> • {displayLocation} ({activity.ipAddress})</span>
                                                        )}
                                                        {loadingLocations[activity.ipAddress] && (
                                                            <span className="text-muted"> (Loading location...)</span>
                                                        )}
                                                    </p>
                                                </div>
                                            </div>
                                        );
                                    })}
                                </div>
                            ) : (
                                <div className="text-center py-4">No recent activity found</div>
                            )}
                        </div>
                    </div>
                    <div className="col-md-2"></div>
                </div>

                {/* Alert component for success/error messages */}
                {defaultAlert && defaultErrorMsg && (
                    <Alert data={defaultErrorMsg} />
                )}
            </div>
        </>
    );
};

export default SecurityTab;
