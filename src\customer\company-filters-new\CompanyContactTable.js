import React, { useState, useContext, useEffect, useRef } from "react";
import '../assests/css/filter/contactstable.css';
import PaginationRender from '../pagination/CompanyRender.js';
import DashboardContext from '../common-files/ContextDashboard.js';
import { postWithToken, PostWithTokenNoCache } from '../common-files/ApiCalls.js';
import { ApiName } from '../common-files/ApiNames';
import ContactModal from './CompanyModel';
import RS_Animation from '../layouts/RS_Animation.js';
import { getSessionItem } from '../common-files/LocalStorage';
import Search_Us from '../layouts/Search_us';
import UseTabStore from "../common-files/useGlobalState";
import loadingGif from '../assests/waiting.gif';
import { useNavigate } from "react-router-dom";
import Alert from "../common-files/alert.js";
import { bulkStoreData, getAllData } from "../common-files/indexedDBUtils.js";
import Loader from "../common-files/Loader.js";

const CompanyTable = ({ paginationDataCount }) => {

    const DB_NAME = 'AdvancedFilterData';
    const STORE_NAME = 'Company';
    const keysToCheck = [
        "company_address_country",
        "company_address_state",
        "company_address_city",
        "company_address_zipcode",
        "company_industries",
        "company_sic_code",
        "company_employee_size",
        "company_annual_revenue_amount",
        "company_tech_keywords_list",
        "company_website",
        "company_type",
        "company_buzzwords_list"
    ];

    const navigate = useNavigate();
    const alertRef = useRef();
    const closeAlertRef = useRef();
    const [loading, setLoading] = useState(true);
    const { dataDC, setDataDC } = useContext(DashboardContext);
    const [data, setData] = useState([]);
    const [showContactModal, setShowContactModal] = useState(true);
    const [foundCounts, setFoundCounts] = useState();
    const [isFilterApplied, setIsFilterApplied] = useState(false);
    const {
        advancedFilter,
        advancedFilterSearchPattern,
        selectedRows,
        unSelectedRows,
        companyKeyword,
        currentSelectedPage,
        viewSelected,
        contactModelId,
        selectedContact,
        sortingBy,
        isLowViewCredits,
        resetPage,
        viewModal,
        pageSize,
        currentPage,
        selectedTab,
        setSelectedTab,
        companyAddressCountry,
        companyAddressState,
        companyAddressCity,
        companyZipCode,
        industryData,
        sicCode,
        companyTechKeyWordsList,
        companyName,
        companyURL,
        companyType,
        applyFilters,
        setSearchPattern,
        setIsLowViewCredits,
        setDefaultAlert,
        setDefaultErrorMsg,
        setSelectedContact,
        setButtonType,
        setIsFiltered,
        setSelectedRows
    } = UseTabStore();

    const localStorageSearchPattern = UseTabStore((state) => state.searchPattern);

    let company_employee_size = null;
    let company_annual_revenue_amount = null;

    let token = null;
    let count = 1;
    var dataPost = {};

    if ("token" in dataDC) token = dataDC.token;
    let user = getSessionItem("user");
    user = user ? JSON.parse(user) : null;
    if (user && "token" in user) token = user.token;

    useEffect(() => {

        setSelectedTab("company");
        // pageSize ? setPageSize(10) : setPageSize(paginationDataCount);
        const loaderElement = document.querySelector('#table-loader');
        const loaderContactTable = document.querySelector('#cust-contact-table');

        if (loaderElement) {
            loaderElement.style.display = 'block'; // Set display to 'none' if the element exists in the DOM
        }
        if (loaderContactTable) {
            loaderContactTable.style.display = 'none'; // Set display to 'none' if the element exists in the DOM
        }

        if ("company_employee_size" in dataDC && dataDC.company_employee_size !== undefined) {
            company_employee_size = dataDC.company_employee_size.map((v) => v);
        }
        if ("company_annual_revenue_amount" in dataDC && dataDC.company_annual_revenue_amount !== undefined) {
            company_annual_revenue_amount = dataDC.company_annual_revenue_amount.map((v) => v);
        }

        const searchPatterns = {
            "company_address_country": !resetPage ? { ...companyAddressCountry } : {},
            "company_address_state": !resetPage ? { ...companyAddressState } : {},
            "company_address_city": !resetPage ? { ...companyAddressCity } : {},
            "company_address_zipcode": !resetPage ? { ...companyZipCode } : {},
            "company_industries": !resetPage ? { ...industryData } : {},
            "company_sic_code": !resetPage ? { ...sicCode } : {},
            "company_employee_size": !resetPage ? { ...company_employee_size } : {},
            "company_annual_revenue_amount": !resetPage ? { ...company_annual_revenue_amount } : {},
            "company_tech_keywords_list": !resetPage ? { ...companyTechKeyWordsList } : {},
            "company_company_name": !resetPage ? { ...companyName } : {},
            "company_website": !resetPage ? { ...companyURL } : {},
            "company_type": !resetPage ? { ...companyType } : {},
            "company_buzzwords_list": !resetPage ? { ...companyKeyword } : {},
        };
        const url = ApiName.esCompanyFilter;
        if (!advancedFilter) {
            setSearchPattern(searchPatterns);
            Object.assign(searchPatterns, {
                searchBy: "company",
                sortBy: sortingBy
            });
            dataPost = {
                page: viewSelected ? currentSelectedPage || 1 : currentPage || 1,
                pageSize: dataDC.membership === "trail" ? 10 : 25,
                searchPattern: searchPatterns
            };

            var hasValues = keysToCheck.some(key => {
                const searchPatternValue = dataPost.searchPattern[key];
                return searchPatternValue && Object.keys(searchPatternValue).length > 0;
            });

            if (!hasValues) {
                const hasValues = keysToCheck.some(key => localStorageSearchPattern[key] && Object.keys(localStorageSearchPattern[key]).length > 0);
                if (localStorageSearchPattern && hasValues) {

                    let empSize = localStorageSearchPattern.company_employee_size;
                    let revenueSize = localStorageSearchPattern.company_annual_revenue_amount;

                    empSize = Object.values(empSize);
                    revenueSize = Object.values(revenueSize);

                    setSearchPattern(localStorageSearchPattern);
                    setDataDC({ ...dataDC, company_employee_size: empSize, company_annual_revenue_amount: revenueSize });

                    dataPost = {
                        page: viewSelected ? currentSelectedPage || 1 : currentPage || 1,
                        pageSize: dataDC.membership === "trail" ? 10 : 25,
                        searchPattern: localStorageSearchPattern
                    };
                }
            } else {
                setSearchPattern(searchPatterns);
                Object.assign(searchPatterns, {
                    searchBy: "company",
                    // withDownloaded: withDownloaded,
                    sortBy: sortingBy
                });
                dataPost = {
                    page: viewSelected ? currentSelectedPage || 1 : currentPage || 1,
                    pageSize: dataDC.membership === "trail" ? 10 : 25,
                    searchPattern: searchPatterns
                };
            }
        } else {
            dataPost = {
                page: advancedFilterSearchPattern.page,
                pageSize: advancedFilterSearchPattern.pageSize,
                searchPattern: advancedFilterSearchPattern.searchPattern,
                maxCompanies: advancedFilterSearchPattern.maxCompanies
            };
        }

        const fetchData = async () => {
            try {
                setLoading(true);
                const res = await PostWithTokenNoCache(url, dataPost);
                if (res != null && "status" in res) {
                    if (res.data.status === 200) {
                        const dataObj = JSON.parse(res.data.data);
                        if (dataObj.records.length < 1) {
                            setFoundCounts(-1);
                            setIsFilterApplied(false);
                        } else {
                            setIsFilterApplied(true);
                            setData(dataObj.records);
                            setLoading(false);
                        }
                        if (loaderElement) {
                            loaderElement.style.display = 'none'; // Set display to 'none' if the element exists in the DOM
                        }
                        if (loaderContactTable) {
                            loaderContactTable.style.display = 'block'; // Set display to 'none' if the element exists in the DOM
                        }
                    }
                }
            } catch (error) {
                setLoading(false);
                setData([]);
                setFoundCounts(-1);

                const loaderElement = document.querySelector('#table-loader');
                const loaderContactTable = document.querySelector('#cust-contact-table');

                setButtonType("error");
                setDefaultErrorMsg("Sorry.! Please try again");
                setDefaultAlert(true);

                if (loaderElement) {
                    loaderElement.style.display = 'none'; // Set display to 'none' if the element exists in the DOM
                }
                if (loaderContactTable) {
                    loaderContactTable.style.display = 'block'; // Set display to 'none' if the element exists in the DOM
                }
            }
        };

        const fetchESData = async () => {
            try {
                setLoading(true);
                cahcedCompanyData(dataPost);
                const res = await PostWithTokenNoCache(url, dataPost);
                if (res != null && "status" in res) {
                    if (res.data.status === 200) {
                        const dataObj = JSON.parse(res.data.data);
                        if (dataObj.totalCount < 1) {
                            setFoundCounts(-1);
                            setIsFilterApplied(true);
                            setLoading(false);
                            setData([]);
                        } else {
                            setFoundCounts(dataObj.totalCount);
                            setIsFilterApplied(true);
                            setData(dataObj.records);
                            setLoading(false);
                        }
                        if (loaderElement) {
                            loaderElement.style.display = 'none'; // Set display to 'none' if the element exists in the DOM
                        }
                        if (loaderContactTable) {
                            loaderContactTable.style.display = 'block'; // Set display to 'none' if the element exists in the DOM
                        }
                    }
                }
            } catch (error) {
                setLoading(false);
                setData([]);
                setFoundCounts(-1);
                setIsFilterApplied(true);

                const loaderElement = document.querySelector('#table-loader');
                const loaderContactTable = document.querySelector('#cust-contact-table');

                setButtonType("error");
                setDefaultErrorMsg("Sorry.! Please try again");
                setDefaultAlert(true);

                if (loaderElement) {
                    loaderElement.style.display = 'none'; // Set display to 'none' if the element exists in the DOM
                }
                if (loaderContactTable) {
                    loaderContactTable.style.display = 'block'; // Set display to 'none' if the element exists in the DOM
                }
            }
        };

        const fetchTotalCount = async () => {
            const totalCount = {
                "searchPattern": dataPost.searchPattern,
            };
            await PostWithTokenNoCache(ApiName.totalContactCounts, totalCount)
                .then(function (response) {
                    if (response.data.status === 200) {
                        const dataObj = JSON.parse(response.data.data);
                        if (dataObj.counts > 0) {
                            setFoundCounts(dataObj.counts);
                            fetchData();
                        } else {
                            setFoundCounts(0);
                            setIsFilterApplied(false);
                            setLoading(false);
                            setData([]);
                            if (loaderElement) {
                                loaderElement.style.display = 'none'; // Set display to 'none' if the element exists in the DOM
                            }
                            if (loaderContactTable) {
                                loaderContactTable.style.display = 'block'; // Set display to 'none' if the element exists in the DOM
                            }
                        }
                    }
                }).catch(function (errors) {
                    if (count == 1) {
                        const loaderElement = document.querySelector('#total_contacts');

                        if (loaderElement) {
                            loaderElement.textContent = 'Retrying...'; // Use textContent to set the text
                        }
                        fetchTotalCount();
                        count++;
                    } else {
                        const loaderElement = document.querySelector('#table-loader');
                        const loaderContactTable = document.querySelector('#cust-contact-table');

                        if (loaderElement) {
                            loaderElement.style.display = 'none'; // Set display to 'none' if the element exists in the DOM
                        }
                        if (loaderContactTable) {
                            loaderContactTable.style.display = 'block'; // Set display to 'none' if the element exists in the DOM
                        }
                        setButtonType("error");
                        setDefaultErrorMsg("Sorry.! Please try again");
                        setDefaultAlert(true);
                        setFoundCounts(-1);
                        setLoading(false);
                    }
                });
        }

        const cahcedCompanyData = async (params) => {
            try {
                await PostWithTokenNoCache(ApiName.cachedCompanyData, params);
            } catch (error) {
                if (error) {
                    return;
                }
            }
        };

        var hasValues = keysToCheck.some(key => {
            const searchPatternValue = dataPost.searchPattern[key];
            return searchPatternValue && Object.keys(searchPatternValue).length > 0;
        });

        if (hasValues && !advancedFilter) {
            fetchESData();
        } else if (hasValues && advancedFilter) {
            fetchAdvancedFilterCompanies();
        } else {
            setIsFiltered(false);
            setFoundCounts(-1);
            setIsFilterApplied(false);
            setLoading(false);
            setData([]);
            if (loaderElement) {
                loaderElement.style.display = 'none'; // Set display to 'none' if the element exists in the DOM
            }
            if (loaderContactTable) {
                loaderContactTable.style.display = 'block'; // Set display to 'none' if the element exists in the DOM
            }
        }
    }, [
        resetPage,
        applyFilters,
        currentPage,
        currentSelectedPage,
        advancedFilter
    ]);

    const fetchAdvancedFilterCompanies = async () => {
        try {
            setLoading(true);

            // Check if data is available in IndexedDB
            const dataFromIndexedDB = await getAllData(DB_NAME, STORE_NAME);

            if (dataFromIndexedDB && dataFromIndexedDB.length > 0) {
                // Data is available in IndexedDB, use it
                let itemsPerPage = dataDC.membership === "trail" ? 10 : 25;

                // Calculate paginated data
                const startIndex = (currentPage - 1) * itemsPerPage;
                const endIndex = startIndex + itemsPerPage;
                const paginatedData = dataFromIndexedDB.slice(startIndex, endIndex);

                // Update selectedRows and unSelectedRows
                let newSelectedRows = [];
                if (selectedRows.length <= 0) {
                    // Case: No selected rows
                    newSelectedRows = dataFromIndexedDB.map((item) => item.id).sort((a, b) => a - b);
                } else {
                    // Case: There are selected rows
                    newSelectedRows = [...selectedRows, ...unSelectedRows].sort((a, b) => a - b);
                }

                // Filter out unSelectedRows from newSelectedRows
                let filteredSelectedRows = newSelectedRows.filter((row) => !unSelectedRows.includes(row));

                // Update Zustand store
                setSelectedRows(filteredSelectedRows);
                setData(paginatedData); // Set paginated data
                setIsFilterApplied(true);
                // setFoundCounts(foundCounts); // Set total count
                setLoading(false);
            } else {
                // Data is not available in IndexedDB, call the API
                const url = ApiName.esAdvancedCompanySearchFilter;
                const res = await PostWithTokenNoCache(url, dataPost);

                if (res != null && "status" in res) {
                    if (res.data.status === 200) {
                        let dataObj = JSON.parse(res.data.data);

                        if (dataObj.length < 1) {
                            setIsFiltered(false);
                            setFoundCounts(-1);
                            setIsFilterApplied(false);
                            setLoading(false);
                        } else {
                            let uniqueRecords = [...new Set(dataObj)]; // Remove duplicates
                            let itemsPerPage = dataDC.membership === "trail" ? 10 : 25;

                            // Save the full dataset to IndexedDB
                            await bulkStoreData(DB_NAME, STORE_NAME, uniqueRecords);

                            // Calculate paginated data
                            const startIndex = (currentPage - 1) * itemsPerPage;
                            const endIndex = startIndex + itemsPerPage;
                            const paginatedData = uniqueRecords.slice(startIndex, endIndex);

                            // Update selectedRows and unSelectedRows
                            let newSelectedRows = [];
                            if (selectedRows.length <= 0) {
                                // Case: No selected rows
                                newSelectedRows = uniqueRecords.map((item) => item.id).sort((a, b) => a - b);
                            } else {
                                // Case: There are selected rows
                                newSelectedRows = [...selectedRows, ...unSelectedRows].sort((a, b) => a - b);
                            }

                            // Filter out unSelectedRows from newSelectedRows
                            let filteredSelectedRows = newSelectedRows.filter((row) => !unSelectedRows.includes(row));

                            // Update Zustand store
                            setSelectedRows(filteredSelectedRows);
                            setData(paginatedData); // Set paginated data
                            setIsFilterApplied(true);
                            // setFoundCounts(uniqueRecords.length); // Set total count
                            setLoading(false);
                        }
                    }
                }
            }

            // Hide loader and show table
            const loaderElement = document.querySelector('#table-loader');
            const loaderContactTable = document.querySelector('#cust-contact-table');

            if (loaderElement) {
                loaderElement.style.display = 'none';
            }
            if (loaderContactTable) {
                loaderContactTable.style.display = 'block';
            }
        } catch (error) {
            if (count < 4) {
                const loaderElement = document.querySelector('#total_contacts');
                if (loaderElement) {
                    loaderElement.textContent = 'Retrying...';
                }
                fetchAdvancedFilterCompanies();
                count++;
            } else {
                const loaderElement = document.querySelector('#table-loader');
                const loaderContactTable = document.querySelector('#cust-contact-table');

                if (loaderElement) {
                    loaderElement.style.display = 'none';
                }
                if (loaderContactTable) {
                    loaderContactTable.style.display = 'block';
                }

                setButtonType("error");
                setDefaultErrorMsg("Sorry.! Please try again");
                setDefaultAlert(true);
                setFoundCounts(-1);
                setLoading(false);
            }
        }
    };

    useEffect(() => {
        fetchSingleContact(contactModelId)
    }, [contactModelId]);

    const urlSingleFetch = ApiName.fetchSingleCompanyDetails;
    async function fetchSingleContactRecord(data, total_balance_contact_view, total_balance_credit) {
        const addActionKey = {
            ...data,
            action: "view",
            searchBy: selectedTab,
        }
        const res = await postWithToken(urlSingleFetch, addActionKey);
        if (res && "status" in res) {
            if (res.data.status === 200) {
                let dataObj = JSON.parse(res.data.data);
                if (dataObj[0] === undefined || dataObj[0] === null) return;
                if (showContactModal) {
                    setIsLowViewCredits(false);
                    setSelectedContact(dataObj[0]);
                    if (total_balance_contact_view != null) {
                        let totalBalanceContactView = total_balance_contact_view;
                        if ("membership" in dataDC && dataDC.membership === "prime") {
                            totalBalanceContactView += 1;
                        } else {
                            totalBalanceContactView -= 1;
                        }
                        const requestBody = {
                            total_balance_contact_view: totalBalanceContactView,
                            total_balance_credit: total_balance_credit
                        };
                        //const updateActiveCreaditsRes = PostWithTokenNoCache(ApiName.updateCreadits, requestBody);
                    }
                }
            }
        }
    }

    const fetchSingleContact = async (dataId) => {
        if (dataId) {
            const data = { "id": dataId };

            if ("membership" in dataDC && dataDC.membership === "prime") {
                fetchSingleContactRecord(data, 0, 0);
            } else {
                fetchSingleContactRecord(data, 0, 0);
            }
        }
    };
    if (loading) {
        return <Loader />;
    }
    return (

        <div id="cust-contact-table" style={{ display: 'block' }}>
            {/* {console.log("foundCounts", foundCounts)}
            {console.log("data", data)}
            {console.log("loading", loading ? "True" : "False")}
            {console.log("isFilterApplied", isFilterApplied ? "True" : "False")} */}
            {
                foundCounts == -1 && !loading && !isFilterApplied ? (
                    <RS_Animation />
                ) : !foundCounts && foundCounts !== -1 && isFilterApplied && !loading ? (
                    <RS_Animation />
                ) : !foundCounts && !isFilterApplied && !loading ? (
                    <RS_Animation />
                ) : foundCounts == -1 && !loading && isFilterApplied ? (
                    <Search_Us />
                ) : foundCounts == 0 && !isFilterApplied && !loading ? (
                    <Search_Us />
                ) : foundCounts && !isFilterApplied && !loading ? (
                    <Search_Us />
                ) : !loading && foundCounts >= 0 && foundCounts != -1 && isFilterApplied ? (
                    <PaginationRender
                        key={foundCounts}
                        foundCounts={foundCounts}
                        pageNumber={currentPage}
                        pageCount={pageSize}
                        data={data}
                        loadingCount={loading}
                        paginationDataCount={paginationDataCount}
                    />
                ) : (
                    <div className="loader-container" style={{ display: 'none', margin: "9rem 0 0 0" }}>
                        <img src={loadingGif} width="300" alt="Loading" className="m-auto d-block" />
                    </div>
                )}
            {selectedContact && viewModal ? <ContactModal isLowViewCredits={isLowViewCredits} /> : (<></>)}
        </div >
    )
}

export default CompanyTable;