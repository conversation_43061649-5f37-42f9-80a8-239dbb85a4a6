import React, { useEffect, useRef } from "react";
import UseTabStore from "./useGlobalState";
import { useNavigate } from "react-router-dom";
import "../assests/css/layouts/reachmax.css"

const WarningAlert = (props) => {
    const currentDomain = window.location.origin;
    const navigate = useNavigate();
    const alertRef = useRef();
    const closeAlertRef = useRef();
    const {
        setButtonType,
        buttonType,
        defaultErrorMsg,
        defaultAlert,
        setDefaultAlert,
        setDefaultErrorMsg,
        setViewModal,
        viewModal,
    } = UseTabStore();

    useEffect(() => {
        document.addEventListener("mousedown", handleOutsideClick);
        document.addEventListener("keydown", handleEscKeyPress);

        return () => {
            document.removeEventListener("mousedown", handleOutsideClick);
            document.removeEventListener("keydown", handleEscKeyPress);
        };
    }, []);

    const handleOutsideClick = (e) => {
        if (!e.target.closest(".modal-content")) {
            close();
        }
    };

    const handleEscKeyPress = (e) => {
        if (e.key === "Escape") {
            close();
        }
    };

    const close = () => {
        setButtonType("");
        setDefaultAlert(false);
        setDefaultErrorMsg("");
        setViewModal(false);
        if (defaultAlert && defaultErrorMsg && buttonType !== "error") closeAlertRef?.current?.click();
    };

    const upgradeNow = () => {
        // close();
        setButtonType("upgrade");
        setDefaultAlert(true);
        setDefaultErrorMsg("upgrade_plan");
    }

    useEffect(() => {
        alertRef.current.click();
    }, []);

    return (
        <>
            <button
                type="button"
                className="btn btn-info btn-md"
                data-toggle="modal"
                data-target="#alertModal"
                ref={alertRef}
                style={{ display: "none" }}
                data-backdrop="false"
            ></button>
            {!defaultAlert && !defaultErrorMsg ? (
               <div className="alert-modal-overlay show" tabIndex="-1" role="dialog" aria-labelledby="customAlertLabel" aria-hidden="true" id="alertModal">
               <div className="alert-modal-dialog">
                 <div className="alert-modal-box">
                   <div className="alert-modal-header">
                     <button
                       ref={closeAlertRef}
                       type="button"
                       className="d-none"
                       data-dismiss="modal"
                       aria-label="Close"
                       onClick={close}
                     />
                     <div onClick={close}>
                       <img src={`${currentDomain}/images/cancl.png`} alt="Close" style={{ cursor: "pointer" }} />
                     </div>
                   </div>
               
                   <div className="alert-modal-icon-row d-flex flex-row justify-content-center">
                     <div>
                       <img src={`${currentDomain}/images/grey-avetar.png`} width="50" alt="Avatar" />
                     </div>
                     <div>
                       <p className="alert-warning-text" style={{ color: "#FFCC00" }}>
                         ! WARNING
                       </p>
                     </div>
                   </div>
               
                   <div className="alert-modal-message">
                     <p>{props.data}</p>
                   </div>
               
                   <div className="alert-modal-footer text-center">
                     <button type="button" className="alert-cancel  px-4" onClick={close}>Close</button>
                   </div>
                 </div>
               </div>
               </div>
            ) : null}
        </>
    );
};

export default WarningAlert;
