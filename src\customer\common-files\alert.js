import React, { useEffect, useRef } from "react";
import UseTabStore from "./useGlobalState";
import { useNavigate } from "react-router-dom";

const Alert = (props) => {
    // Get the current domain name
    const currentDomain = window.location.origin;

    const navigate = useNavigate();
    const alertRef = useRef();
    const closeAlertRef = useRef();
    const { setButtonType, buttonType, defaultErrorMsg, defaultAlert, setDefaultAlert, setDefaultErrorMsg, setViewModal, viewModal } = UseTabStore();

    useEffect(() => {
        // Add event listeners when the component mounts
        document.addEventListener('mousedown', handleOutsideClick);
        document.addEventListener('keydown', handleEscKeyPress);

        // Remove event listeners when the component unmounts
        return () => {
            document.removeEventListener('mousedown', handleOutsideClick);
            document.removeEventListener('keydown', handleEscKeyPress);
        };
    }, []);

    const handleOutsideClick = (e) => {
        // Check if the click is outside the modal
        if (!e.target.closest('.modal')) {
            close();
        }
    };

    const handleEscKeyPress = (e) => {
        // Check if the pressed key is ESC
        if (e.key === 'Escape') {
            close();
        }
    };


     const close = () => {
        setButtonType("");
        setDefaultAlert(false);
        setDefaultErrorMsg("");
        setViewModal(false);
        if (defaultAlert && defaultErrorMsg && defaultErrorMsg == "error")
            closeAlertRef?.current?.click();
    }

    useEffect(() => {
        if (defaultAlert && defaultErrorMsg && defaultErrorMsg == "error") {
            alertRef?.current?.click();
        }
    }, [])
    return (
        <>
            <button
                type="button"
                className="btn btn-info btn-md"
                data-toggle="modal"
                data-target="#alertModal"
                ref={alertRef}
                style={{ display: 'none' }} // Hide the button, it's just for triggering click programmatically
                data-backdrop="true"
            ></button>
            {defaultAlert && defaultErrorMsg && buttonType == "error" ? (
               <div className="custom-modal-overlay show" tabIndex="-1" role="dialog" aria-labelledby="customModalLabel" aria-hidden="true" id="alertModal">
               <div className="custom-modal-dialog">
                 <div className="custom-modal-content">
                   <div className="custom-modal-header">
                     <button
                       ref={closeAlertRef}
                       type="button"
                       className="d-none"
                       data-dismiss="modal"
                       aria-label="Close"
                       onClick={close}
                     />
                     <div onClick={close}>
                       <img src={`${currentDomain}/images/cancl.png`} style={{ cursor: "pointer" }} alt="Close" />
                     </div>
                   </div>
             
                   <div className="custom-modal-body d-flex justify-content-center align-items-center gap-3">
                     <img src={`${currentDomain}/images/grey-avetar.png`} width="50" alt="Avatar" />
                     <p className="custom-modal-icon">!</p>
                     <p className="custom-modal-title">An Error Occurred</p>
                   </div>
             
                   <div className="custom-modal-message text-center">
                     <p>{props.data}</p>
                   </div>
             
                   <div className="custom-modal-footer text-center">
                     <button type="button" className="alert-cancel px-4" onClick={close}>Close</button>
                   </div>
                 </div>
               </div>
             </div>
             
            ) : (
                <></>
            )}

        </>
    )
}

export default Alert;